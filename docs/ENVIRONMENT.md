# Environment Configuration

This project supports multiple environments:

- **Development** (local): Default development environment
- **Test**: Test environment
- **Gray** (灰度): Gray/Canary environment
- **Production**: Production environment

## Environment Configuration Files

The environment-specific configurations are stored in the following files:

- `src/config/env.development.ts` - Development environment
- `src/config/env.test.ts` - Test environment
- `src/config/env.gray.ts` - Gray environment
- `src/config/env.production.ts` - Production environment

Each file exports a default object with environment-specific settings.

## Menu Configuration

The application uses a configuration-based approach for menus instead of API calls. The menu configuration is defined in `src/config/menu.config.ts` and supports different menu structures for different environments:

- **Development/Test/Gray**: Full menu with all features and components
- **Production**: Simplified menu with only essential features

To modify the menu structure:

1. Edit the appropriate menu list in `src/config/menu.config.ts`:
   - `baseMenuList`: Common menu items for all environments
   - `devMenuList`: Menu items for development environments
   - `prodMenuList`: Menu items for production environment

2. The menu structure follows this interface:
```typescript
interface MenuItem {
  code: string;
  label: {
    zh_CN: string;
    en_US: string;
  };
  icon: string;
  path: string;
  children?: MenuItem[];
}
```

## Notice Configuration

The application uses a configuration-based approach for notifications instead of API calls. The notice configuration is defined in `src/config/notice.config.ts` and supports different notification items for different environments:

- **Development/Test/Gray**: Full set of notification items for testing
- **Production**: Reduced set of notification items

To modify the notification items:

1. Edit the appropriate notice list in `src/config/notice.config.ts`:
   - `baseNoticeList`: Common notification items for all environments
   - `devNoticeList`: Extended notification items for development environments

2. The notice structure follows this interface:
```typescript
interface Notice<T> {
  id: string;
  title: string;
  description?: string;
  datetime: string;
  type: T;
  read?: boolean;
  avatar?: string;
  extra?: string;
  status?: string;
  clickClose?: boolean;
}
```

## Mock API Mode

The application supports a mock API mode that allows you to use a mock API server for development and testing. This is useful when the backend API is not available or when you want to test specific scenarios.

### Environment Support for Mock API

Mock API mode is only available in the following environments:
- ✅ **Development** (local)
- ✅ **Test**
- ❌ **Gray** (灰度) - Not supported
- ❌ **Production** - Not supported

In Gray and Production environments, all requests will use the real API endpoints regardless of mock settings.

### Using Mock API Mode

There are two ways to enable mock API mode:

1. **Globally via environment variable**:
   - Set `VITE_USE_MOCK_API=true` in `.env.local` file
   - Use npm scripts to toggle mock API mode:
     ```bash
     # Enable mock API mode
     npm run use:mock
     
     # Disable mock API mode
     npm run use:no-mock
     
     # Start development server with mock API mode enabled
     npm run dev:mock
     ```

2. **Per-request basis**:
   - Add `mock: true` to the request config:
     ```typescript
     import { request } from '@/api/request';
     
     // Example: Using mock API for a specific request
     const loginResult = await request('post', '/user/login', data, {
       mock: true,
     });
     
     // Example: Using mock API for a GET request with parameters
     const userList = await request('get', '/business/list', { page: 1, size: 10, mock: true });
     ```

### Mock API Configuration

The mock API server URL is configured in `src/api/request.ts`:
```typescript
const MOCK_API_URL = 'http://127.0.0.1:4523/m1/345161-308456-default';
```

You can update this URL to point to your own mock API server.

## Switching Environments

You can switch between environments using the following npm scripts:

```bash
# Switch to development environment
npm run env:dev

# Switch to test environment
npm run env:test

# Switch to gray environment
npm run env:gray

# Switch to production environment
npm run env:prod
```

## Running in Specific Environment

You can directly run the application in a specific environment:

```bash
# Run in development environment (default)
npm run dev

# Run in test environment
npm run dev:test

# Run in gray environment
npm run dev:gray

# Run in production environment
npm run dev:prod
```

## Using Test API in Development Environment

The development environment supports connecting to both local and test backend APIs. You can:

1. **Use the ApiSwitcher component**: Include the `ApiSwitcher` component in your layout to toggle between local and test APIs:

```tsx
import ApiSwitcher from '@/components/ApiSwitcher';

// In your layout component
<ApiSwitcher />
```

2. **Programmatically switch APIs**: Use the `switchToTestApi` function in your code:

```tsx
import { switchToTestApi } from '@/api/request';

// Switch to test API
switchToTestApi(true);

// Switch back to local API
switchToTestApi(false);
```

3. **Access API URLs directly**:

```tsx
import { baseApi, testApi } from '@/config/env';

console.log('Local API:', baseApi);
console.log('Test API:', testApi);
```

## Building for Specific Environment

You can build the application for a specific environment:

```bash
# Build for development environment (default)
npm run build

# Build for test environment
npm run build:test

# Build for gray environment
npm run build:gray

# Build for production environment
npm run build:prod
```

## Environment Variables in Code

You can access the current environment and configuration in your code by importing from `@/config/env`:

```typescript
import { baseApi, testApi, currentEnv, EnvType } from '@/config/env';

// Check current environment
if (currentEnv === EnvType.Production) {
  // Production-specific code
}

// Use environment-specific API URL
console.log(`API URL: ${baseApi}`);

// In development, you can also access the test API
if (currentEnv === EnvType.Development && testApi) {
  console.log(`Test API URL: ${testApi}`);
}
```

## Adding New Environment Variables

To add new environment variables:

1. Update the `EnvConfig` interface in `src/config/env.ts`
2. Add the new variable to each environment-specific file
3. Export the variable in `src/config/env.ts` 