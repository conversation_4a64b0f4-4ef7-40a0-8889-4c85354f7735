# API 配置文档

本文档详细说明了项目的API配置和使用方法。

## 环境与API地址

项目支持多种环境，每种环境使用不同的API地址：

| 环境 | 命令 | API地址 | Mock支持 |
| --- | --- | --- | --- |
| 开发环境 | `npm run dev` | https://newtest-gateway.ifengqun.com | ✅ 支持单个接口mock |
| Mock环境 | `npm run dev:mock` | http://127.0.0.1:4523/m1/345161-308456-default | ✅ 全部接口mock |
| 测试环境 | `npm run dev:test` | https://newtest-gateway.ifengqun.com | ❌ 不支持mock |
| 灰度环境 | `npm run dev:gray` | https://fq-gw-gray.ifengqun.com | ❌ 不支持mock |
| 生产环境 | `npm run dev:prod` | https://newprod.ifengqun.com | ❌ 不支持mock |

## Mock API

### Mock API地址

Mock API使用Apifox提供的接口：
```
http://127.0.0.1:4523/m1/345161-308456-default
```

### Mock API使用方式

#### 1. 全局Mock模式

使用 `npm run dev:mock` 启动项目，所有接口都将使用Mock数据，无需在每个接口中单独设置。

#### 2. 单个接口Mock

在接口方法中添加 `mock: true` 参数，只有添加了这个参数的接口会使用Mock数据。

```typescript
// 示例：POST请求使用mock数据
export const apiLogin = (data: LoginParams) => request<LoginResult>('post', '/user/login', data, {
  mock: true,
});

// 示例：GET请求使用mock数据
export const getBusinessUserList = (params: any) =>
  request<PageData<BusinessUser>>('get', '/business/list', { ...params, mock: true });

// 示例：将mock参数与其他参数一起传递
export const getBusinessUserList = (params: any) => {
  const { page, size, ...restParams } = params;
  return request<PageData<BusinessUser>>('get', '/business/list', { 
    page, 
    size, 
    ...restParams 
  }, { 
    mock: true 
  });
};
```

### 环境限制

Mock API的使用受到环境的限制：

- **开发环境（dev）**：支持单个接口mock，只有添加了`mock: true`的接口会使用Mock数据
- **Mock环境（dev:mock）**：所有接口都使用Mock数据
- **测试环境（dev:test）**：不支持mock，即使设置了`mock: true`也会使用真实API
- **灰度环境（dev:gray）**：不支持mock，即使设置了`mock: true`也会使用真实API
- **生产环境（dev:prod）**：不支持mock，即使设置了`mock: true`也会使用真实API

## 请求配置

项目使用Axios作为HTTP客户端，并对其进行了封装。请求配置在`src/api/request.ts`文件中。

### 请求方法

```typescript
export const request = <T = any>(
  method: Lowercase<Method>,  // 请求方法：'get', 'post', 'put', 'delete'等
  url: string,                // 请求URL
  data?: any,                 // 请求数据或参数
  config?: RequestConfig,     // 请求配置，可以包含mock: true来使用Mock数据
): MyResponse<T> => {
  // 方法实现...
};
```

### 请求配置接口

```typescript
// 扩展AxiosRequestConfig以包含mock选项
export interface RequestConfig extends AxiosRequestConfig {
  mock?: boolean;  // 是否使用Mock数据
}
```

### 响应类型

```typescript
export type Response<T = any> = {
  status: boolean;   // 请求状态
  message: string;   // 响应消息
  result: T;         // 响应数据
};

export type MyResponse<T = any> = Promise<Response<T>>;
```

## 使用示例

### 基本使用

```typescript
// 定义API方法
export const getUserInfo = (userId: string) => 
  request<UserInfo>('get', `/user/${userId}`);

// 使用API方法
const fetchUserInfo = async () => {
  try {
    const { status, result, message } = await getUserInfo('123');
    if (status) {
      // 处理成功响应
      console.log(result);
    } else {
      // 处理失败响应
      console.error(message);
    }
  } catch (error) {
    // 处理异常
    console.error(error);
  }
};
```

### 使用Mock数据

```typescript
// 定义使用Mock数据的API方法
export const getUserList = (params: UserQueryParams) => 
  request<PageData<UserInfo>>('get', '/user/list', params, { mock: true });

// 使用API方法
const fetchUserList = async () => {
  const params = { page: 1, size: 10, status: 'active' };
  const { status, result } = await getUserList(params);
  if (status) {
    // 处理成功响应
    console.log(result.list);
    console.log(`总记录数: ${result.total}`);
  }
};
```

## 最佳实践

1. **API方法集中管理**：将API方法按功能模块分类，集中在`src/api`目录下管理
   
2. **类型安全**：为所有API请求和响应定义TypeScript接口，提高代码质量和开发效率
   
3. **环境感知**：在开发过程中注意当前环境对Mock API的支持情况
   
4. **错误处理**：统一处理API错误，包括网络错误、服务器错误和业务错误
   
5. **请求拦截**：使用请求拦截器添加通用请求头、认证信息等
   
6. **响应拦截**：使用响应拦截器统一处理响应格式、错误码等 