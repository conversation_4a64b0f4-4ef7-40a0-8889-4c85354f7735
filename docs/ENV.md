# 环境配置指南

本文档详细说明了项目的环境配置系统。

## 环境类型

项目支持以下环境类型：

| 环境类型 | 说明 | 环境变量值 |
| --- | --- | --- |
| 开发环境 | 用于本地开发 | `development` |
| 测试环境 | 用于测试和QA | `test` |
| 灰度环境 | 用于灰度发布 | `gray` |
| 生产环境 | 用于线上生产 | `production` |

## 环境变量

项目使用以下环境变量：

| 环境变量 | 说明 | 可能的值 |
| --- | --- | --- |
| `VITE_APP_ENV` | 当前环境类型 | `development`, `test`, `gray`, `production` |
| `VITE_USE_MOCK_API` | 是否使用Mock API | `true`, `false` |
| `VITE_USE_TEST_API` | 是否使用测试API | `true`, `false` |

## 启动不同环境

### 开发环境

```bash
# 默认开发环境（使用test环境API，支持单个接口mock）
npm run dev

# 使用Mock API（所有接口都使用mock数据）
npm run dev:mock

# 使用测试环境
npm run dev:test

# 使用灰度环境
npm run dev:gray

# 使用生产环境
npm run dev:prod
```

### 构建不同环境

```bash
# 构建默认版本
npm run build

# 构建测试环境版本
npm run build:test

# 构建灰度环境版本
npm run build:gray

# 构建生产环境版本
npm run build:prod
```

## 环境配置文件

项目使用以下文件进行环境配置：

### 环境配置文件

- `src/config/env.ts` - 主环境配置文件
- `src/config/env.development.ts` - 开发环境特定配置
- `src/config/env.test.ts` - 测试环境特定配置
- `src/config/env.gray.ts` - 灰度环境特定配置
- `src/config/env.production.ts` - 生产环境特定配置

### 示例：环境特定配置文件

```typescript
// src/config/env.development.ts
export default {
  baseApi: 'https://newtest-gateway.ifengqun.com',
  testApi: 'http://test-api.example.com/api'
};
```

## 环境切换脚本

项目使用以下脚本进行环境切换：

- `scripts/switch-env.js` - 切换环境
- `scripts/use-mock-api.js` - 启用/禁用Mock API
- `scripts/check-env.js` - 检查当前环境变量

### 环境切换原理

当运行环境切换命令时，脚本会在项目根目录创建或修改 `.env.local` 文件，设置相应的环境变量。

例如，运行 `npm run dev:test` 会：

1. 执行 `node scripts/switch-env.js test`
2. 在 `.env.local` 文件中设置 `VITE_APP_ENV=test`
3. 启动开发服务器 `vite`

## 在代码中使用环境配置

```typescript
import { baseApi, currentEnv, EnvType } from '@/config/env';

// 检查当前环境
if (currentEnv === EnvType.Development) {
  console.log('当前是开发环境');
}

// 使用环境特定的API地址
console.log(`API地址: ${baseApi}`);

// 根据环境执行不同逻辑
switch (currentEnv) {
  case EnvType.Development:
    // 开发环境特定逻辑
    break;
  case EnvType.Test:
    // 测试环境特定逻辑
    break;
  case EnvType.Gray:
    // 灰度环境特定逻辑
    break;
  case EnvType.Production:
    // 生产环境特定逻辑
    break;
}
```

## 环境检测

如果您不确定当前的环境配置，可以运行以下命令检查：

```bash
node scripts/check-env.js
```

这将显示：
- `.env.local` 文件内容（如果存在）
- 当前的 `NODE_ENV` 值
- 可用的环境切换命令 