{"typescript.tsdk": "node_modules/typescript/lib", "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "eslint.options": {"extensions": [".js", ".jsx", ".ts", ".tsx"]}, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.formatOnSave": true, "[javascript]": {"editor.formatOnSave": false}, "[javascriptreact]": {"editor.formatOnSave": false}, "[typescript]": {"editor.formatOnSave": false}, "[typescriptreact]": {"editor.formatOnSave": false}}