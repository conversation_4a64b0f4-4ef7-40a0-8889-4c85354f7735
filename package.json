{"name": "react-antd-admin", "version": "0.1.0", "private": true, "dependencies": {"@reduxjs/toolkit": "^1.4.0", "antd": "^5.25.4", "axios": "^0.24.0", "classnames": "^2.5.1", "dayjs": "^1.11.13", "driver.js": "^0.9.8", "history": "^5.0.1", "lodash": "^4.17.21", "mockjs": "^1.1.0", "query-string": "^7.0.1", "react": "^17.0.0", "react-dom": "^17.0.0", "react-intl": "^7.1.11", "react-redux": "^7.2.6", "react-router-dom": "^6.0.0", "recharts": "^2.1.13"}, "scripts": {"dev": "node scripts/switch-env.js development && vite", "dev:mock": "node scripts/use-mock-api.js true && vite", "dev:test": "node scripts/switch-env.js test && vite", "dev:gray": "node scripts/switch-env.js gray && vite", "dev:prod": "node scripts/switch-env.js production && vite", "build": "tsc && vite build", "build:test": "node scripts/switch-env.js test && tsc && vite build", "build:gray": "node scripts/switch-env.js gray && tsc && vite build", "build:prod": "node scripts/switch-env.js production && tsc && vite build", "serve": "vite preview", "lint": "eslint . --ext js,ts,tsx", "format": "prettier --write **/*.{js,ts,tsx} && eslint . --ext js,ts,tsx --fix", "env:dev": "node scripts/switch-env.js development", "env:test": "node scripts/switch-env.js test", "env:gray": "node scripts/switch-env.js gray", "env:prod": "node scripts/switch-env.js production", "use:mock": "node scripts/use-mock-api.js true", "use:no-mock": "node scripts/use-mock-api.js false", "check-env": "node scripts/check-env.js"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@rollup/plugin-babel": "^5.3.0", "@types/lodash": "^4.17.17", "@types/mockjs": "^1.0.2", "@types/node": "^16.11.6", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@types/react-redux": "^7.1.20", "@types/recharts": "^1.8.13", "@types/webpack-env": "^1.15.0", "@typescript-eslint/eslint-plugin": "^5.3.0", "@typescript-eslint/parser": "^5.3.0", "@vitejs/plugin-react": "^1.1.0", "@vitejs/plugin-react-refresh": "^1.3.6", "babel-plugin-import": "^1.13.3", "eslint": "^8.1.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-simple-import-sort": "^10.0.0", "less": "^4.1.2", "prettier": "^2.4.1", "typescript": "^4.3.2", "vite": "^2.6.4", "vite-plugin-imp": "^2.0.10", "vite-plugin-svgr": "^0.6.0"}}