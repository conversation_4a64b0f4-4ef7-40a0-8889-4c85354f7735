// Environment configuration

// Environment types
export enum EnvType {
  Development = 'development',
  Test = 'test',
  Gray = 'gray',
  Production = 'production',
}

// API base URLs for different environments
interface EnvConfig {
  baseApi: string;
  testApi?: string; // Optional test API URL for development environment
  env: EnvType;
}

// Get current environment from VITE_APP_ENV, import.meta.env.MODE, or NODE_ENV
// Ensure it's one of our defined environments or default to development
const getEnvironment = (): string => {
  // First check for VITE_APP_ENV which is set by our switch-env.js script
  const appEnv = import.meta.env.VITE_APP_ENV as string | undefined;

  if (appEnv) {
    console.log(`Using environment from VITE_APP_ENV: ${appEnv}`);

    return appEnv;
  }

  // Fall back to Vite's MODE or Node's NODE_ENV
  const defaultEnv = import.meta.env.MODE || process.env.NODE_ENV || 'development';

  console.log(`No VITE_APP_ENV found, using default environment: ${defaultEnv}`);

  return defaultEnv;
};

const env = getEnvironment();

// Check if we should use test API in development
const useTestApiInDev = import.meta.env.VITE_USE_TEST_API === 'true';

// Create a self-executing async function to handle dynamic imports
const getEnvConfig = async (): Promise<Record<string, EnvConfig>> => {
  // Import environment-specific configuration
  let envSpecificConfig: { default: { baseApi: string; testApi?: string } };

  try {
    // Dynamic import based on environment
    switch (env) {
      case 'development':
        envSpecificConfig = await import('./env.development');
        break;
      case 'test':
        envSpecificConfig = await import('./env.test');
        break;
      case 'gray':
        envSpecificConfig = await import('./env.gray');
        break;
      case 'production':
        envSpecificConfig = await import('./env.production');
        break;
      default:
        envSpecificConfig = await import('./env.development');
    }
  } catch (error) {
    console.error('Failed to load environment config:', error);
    // Fallback to default config
    envSpecificConfig = {
      default: {
        baseApi: 'http://localhost:8080/api',
        testApi: 'http://test-api.example.com/api',
      },
    };
  }

  // Environment configurations with fallbacks
  return {
    development: {
      baseApi: envSpecificConfig.default.baseApi || 'http://localhost:8080/api',
      testApi: envSpecificConfig.default.testApi || 'http://test-api.example.com/api',
      env: EnvType.Development,
    },
    test: {
      baseApi: envSpecificConfig.default.baseApi || 'http://test-api.example.com/api',
      env: EnvType.Test,
    },
    gray: {
      baseApi: envSpecificConfig.default.baseApi || 'http://gray-api.example.com/api',
      env: EnvType.Gray,
    },
    production: {
      baseApi: envSpecificConfig.default.baseApi || 'http://api.example.com/api',
      env: EnvType.Production,
    },
  };
};

// For now, use static configuration and update it when the dynamic config is loaded
let EnvConfigs: Record<string, EnvConfig> = {
  development: {
    baseApi: 'https://newtest-gateway.ifengqun.com',
    testApi: 'http://test-api.example.com/api',
    env: EnvType.Development,
  },
  test: {
    baseApi: 'https://newtest-gateway.ifengqun.com',
    env: EnvType.Test,
  },
  gray: {
    baseApi: 'https://fq-gw-gray.ifengqun.com',
    env: EnvType.Gray,
  },
  production: {
    baseApi: 'https://newprod.ifengqun.com',
    env: EnvType.Production,
  },
};

// Get the current environment config or default to development
export const envConfig: EnvConfig = EnvConfigs[env] || EnvConfigs.development;

// Export individual configuration values for convenience
export const testApi = envConfig.testApi; // Export testApi if available
export const currentEnv = envConfig.env;

// If we're in development and should use test API, use that instead of baseApi
export const baseApi = currentEnv === EnvType.Development && useTestApiInDev && testApi ? testApi : envConfig.baseApi;

// Log environment information
console.log(`Environment: ${currentEnv}, API: ${baseApi}`);
console.log(`Raw environment value: ${env}`);

if (testApi && currentEnv === EnvType.Development) {
  console.log(`Test API available: ${testApi}`);

  if (useTestApiInDev) {
    console.log('Using Test API in development mode');
  }
}

// Update configuration asynchronously
getEnvConfig().then(configs => {
  EnvConfigs = configs;
  console.log('Environment configuration updated dynamically');
});
