/**
 * <AUTHOR>
 * @Title 侧边菜单栏
 */

import type { MenuList } from '@/types/layout/menu';

export const menuList: MenuList = [
  {
    /** 客户管理 */
    key: 'customer',
    // 多语言key
    label: 'menu.customerManage',
    path: '/customer',
    children: [
      {
        /** 个人客户 */
        key: '/customer/personal',
        label: 'menu.personalCustomer',
        path: '/customer/personal',
      },
    ],
  },
  {
    /** 产品管理 */
    key: 'product',
    label: 'menu.productManage',
    path: '/product',
    children: [
      {
        /** 产品信息 */
        key: '/products',
        label: 'menu.productInfo',
        path: '/products',
      },
      {
        /** 产品公告 */
        key: '/product/announcement',
        label: 'menu.productAnnouncement',
        path: '/product/announcement',
      },
    ],
  },
  {
    /** 订单管理 */
    key: 'order',
    label: 'menu.orderManage',
    path: '/order',
    children: [
      {
        /** 认购申请 */
        key: '/order/subscribe',
        label: 'menu.subscribeApplication',
        path: '/order/subscribe',
      },
      {
        /** 份额查询 */
        key: '/order/query',
        label: 'menu.quotaQuery',
        path: '/order/query',
      },
    ],
  },
];
