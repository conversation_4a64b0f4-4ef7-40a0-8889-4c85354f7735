/**
 * @author: limengyi
 * @description: 详情页顶部栏
 */

import type { FC } from 'react';

import { ArrowLeftOutlined } from '@ant-design/icons';
import { Button, Card } from 'antd';
import { useNavigate } from 'react-router-dom';

interface DetailHeaderProps {
  /** 页面标题 */
  title: string;
  handleBack?: () => void;
  style?: React.CSSProperties;
  className?: string;
}

const DetailHeader: FC<DetailHeaderProps> = ({ title, handleBack, style, className }) => {
  const navigate = useNavigate();

  const goBack = () => {
    if (handleBack) {
      handleBack();
    } else {
      navigate(-1);
    }
  };

  return (
    <Card className={`detail-header ${className}`} style={style}>
      <div className="back-button" onClick={goBack}>
        <Button icon={<ArrowLeftOutlined />} />
        <span style={{ marginLeft: 8, fontSize: 16, fontWeight: 'bold' }}>{title}</span>
      </div>
    </Card>
  );
};

export default DetailHeader;
