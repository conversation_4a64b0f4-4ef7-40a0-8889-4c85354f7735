import { Form, type FormItemProps } from 'antd';
import { type ReactNode, useMemo } from 'react';

import { useDescriptionsFormContext } from './DescriptionsFormContext';

/** DescriptionsFormField 模式选项 */
const _DescriptionsFormFieldModes = ['edit', 'read'] as const;

export type DescriptionsFormFieldModes = (typeof _DescriptionsFormFieldModes)[number];

export interface DescriptionsFormFieldProps {
  /** 组件的模式，如果不设置，则会从 DescriptionsForm 中获取 */
  mode?: DescriptionsFormFieldModes;
  /** 自定义 mode=read 下的 dom 表现，只是单纯的表现形式 */
  render?: () => ReactNode;
  /** 自定义 mode=edit 下的 dom 表现，一般用于渲染编辑框 */
  renderFormItem?: () => ReactNode;
  /** Form.Item 的 name，一般用于绑定表单数据 */
  formItemName?: FormItemProps['name'];
  /** Form.Item 的配置 */
  formItemProps?: FormItemProps;
}

const DescriptionsFormField = (props: DescriptionsFormFieldProps) => {
  const { mode, formItemProps, formItemName, render, renderFormItem } = props;
  const { mode: parentMode } = useDescriptionsFormContext();

  const fieldMode = useMemo(() => {
    return mode || parentMode || 'read';
  }, [mode, parentMode]);

  return (
    <>
      {fieldMode === 'edit' ? (
        <Form.Item style={{ margin: 0 }} {...formItemProps} name={formItemName}>
          {renderFormItem?.()}
        </Form.Item>
      ) : (
        render?.()
      )}
    </>
  );
};

export default DescriptionsFormField;
