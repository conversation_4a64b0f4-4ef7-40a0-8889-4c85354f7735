import type { DescriptionsFormFieldModes } from './DescriptionsFormField';

import { Descriptions, type DescriptionsProps, type FormProps } from 'antd';
import { Form } from 'antd';
import { type DescriptionsItemType } from 'antd/es/descriptions';
import { type ReactNode } from 'react';

import { DescriptionsFormContext } from './DescriptionsFormContext';

export interface DescriptionsFormItem extends Omit<DescriptionsItemType, 'children'> {
  // children?: typeof DescriptionsFormField;
  children?: ReactNode;
}

export interface DescriptionsFormProps extends Omit<DescriptionsProps, 'items'> {
  /** 数据源 */
  // dataSource?: any;
  /** 描述列表项内容 */
  items?: DescriptionsFormItem[];
  /** 组件的模式 */
  mode?: DescriptionsFormFieldModes;
  /** 表单配置 */
  formProps?: FormProps;
}

const FormWrapper = (props: Pick<DescriptionsFormProps, 'formProps' | 'mode'> & { children?: ReactNode }) => {
  return props.mode === 'edit' ? <Form {...props.formProps}>{props.children}</Form> : <>{props.children}</>;
};

const DescriptionsForm = (props: DescriptionsFormProps) => {
  const { mode = 'read', items, formProps, bordered = true, ...restProps } = props;

  return (
    <DescriptionsFormContext.Provider value={{ mode }}>
      <FormWrapper mode={mode} formProps={formProps}>
        <Descriptions items={items as DescriptionsItemType[]} bordered={bordered} {...restProps} />
      </FormWrapper>
    </DescriptionsFormContext.Provider>
  );
};

export default DescriptionsForm;
