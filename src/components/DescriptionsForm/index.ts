import InternalDescriptionsForm, { type DescriptionsFormItem, type DescriptionsFormProps } from './DescriptionsForm';
import DescriptionsForm<PERSON>ield, {
  type DescriptionsFormFieldModes,
  type DescriptionsFormFieldProps,
} from './DescriptionsFormField';

type InternalDescriptionsFormType = typeof InternalDescriptionsForm;

type CompoundedComponent = InternalDescriptionsFormType & {
  Field: typeof DescriptionsFormField;
};

const DescriptionsForm = InternalDescriptionsForm as CompoundedComponent;

DescriptionsForm.Field = DescriptionsFormField;

export type { DescriptionsFormFieldModes, DescriptionsFormFieldProps, DescriptionsFormItem, DescriptionsFormProps };

export default DescriptionsForm;
