import { Badge, Space, Switch, Typography } from 'antd';
import React, { useEffect, useState } from 'react';

import { switchToTestApi } from '@/api/request';
import { baseApi, currentEnv, EnvType, testApi } from '@/config/env';

const { Text } = Typography;

/**
 * Component to switch between local and test APIs in development environment
 * This component will only render in development environment
 */
const ApiSwitcher: React.FC = () => {
  // Initialize state from environment variable
  const [useTestApi, setUseTestApi] = useState(import.meta.env.VITE_USE_TEST_API === 'true');

  // Effect to initialize from localStorage if available
  useEffect(() => {
    const storedPreference = localStorage.getItem('useTestApi');

    if (storedPreference) {
      const shouldUseTestApi = storedPreference === 'true';

      setUseTestApi(shouldUseTestApi);
      switchToTestApi(shouldUseTestApi);
    }
  }, []);

  // Don't render in non-development environments
  if (currentEnv !== EnvType.Development || !testApi) {
    return null;
  }

  const handleChange = (checked: boolean) => {
    setUseTestApi(checked);
    switchToTestApi(checked);

    // Store the preference in localStorage for persistence across page reloads
    localStorage.setItem('useTestApi', checked ? 'true' : 'false');
  };

  return (
    <div style={{ padding: '8px 16px', background: '#f0f0f0', borderRadius: '4px', marginBottom: '16px' }}>
      <Space>
        <Badge status={useTestApi ? 'processing' : 'default'} text={<Text strong>API:</Text>} />
        <Switch checkedChildren="Test API" unCheckedChildren="Local API" checked={useTestApi} onChange={handleChange} />
        <Text type="secondary">{useTestApi ? testApi : baseApi}</Text>
      </Space>
    </div>
  );
};

export default ApiSwitcher;
