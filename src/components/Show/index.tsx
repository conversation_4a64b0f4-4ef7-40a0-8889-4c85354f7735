/**
 * @Owners PGee
 * @Title Show 组件
 */
type TShowProps<T> = {
  when: T | false | null | undefined;
  children: React.ReactNode | ((item: T) => React.ReactNode);
  fallback?: React.ReactNode;
};

// eslint-disable-next-line @typescript-eslint/no-unnecessary-type-constraint,@typescript-eslint/no-explicit-any
const Show = <T extends any>({ when, fallback, children }: TShowProps<T>) => {
  if (when) {
    return typeof children === 'function' ? <>{children(when)}</> : <>{children}</>;
  }

  return <>{fallback}</>;
};

export default Show;
