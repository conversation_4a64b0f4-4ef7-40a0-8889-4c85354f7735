import { Al<PERSON>, Card, Divider, Space, Switch, Tag, Typography } from 'antd';
import React, { useEffect, useState } from 'react';

import { currentEnv, EnvType } from '@/config/env';

import ApiSwitcher from './ApiSwitcher';

const { Title, Text } = Typography;

// Check if current environment allows mock API
const isMockAllowed = () => {
  // Only allow mock API in development and test environments
  return currentEnv === EnvType.Development || currentEnv === EnvType.Test;
};

/**
 * Development tools component
 * Only visible in development environment
 */
const DevTools: React.FC = () => {
  // Get mock API mode status from environment variable
  const [useMockApi, setUseMockApi] = useState(import.meta.env.VITE_USE_MOCK_API === 'true');
  // Get raw environment value
  const rawEnv = import.meta.env.VITE_APP_ENV || import.meta.env.MODE || 'development';

  // Only render in development environment
  if (currentEnv !== EnvType.Development) {
    return null;
  }

  // Handle mock API toggle
  const handleMockApiToggle = (checked: boolean) => {
    // This only updates the UI, to actually change the mode you need to restart the app
    setUseMockApi(checked);
    localStorage.setItem('preferMockApi', checked ? 'true' : 'false');
  };

  return (
    <Card
      title="Development Tools"
      size="small"
      style={{ marginBottom: 16 }}
      extra={<Text type="secondary">Only visible in development</Text>}
    >
      <ApiSwitcher />

      <Divider style={{ margin: '8px 0' }} />

      <Space direction="vertical" style={{ width: '100%' }}>
        <Space style={{ marginBottom: 8 }}>
          <Text strong>Mock API:</Text>
          <Switch
            checkedChildren="Enabled"
            unCheckedChildren="Disabled"
            checked={useMockApi}
            onChange={handleMockApiToggle}
            disabled={!isMockAllowed()}
          />
          <Text type="secondary">{useMockApi ? 'Using mock API server' : 'Using real API'}</Text>
        </Space>

        {useMockApi && (
          <div style={{ marginBottom: 8, marginLeft: 16 }}>
            <Text type="secondary">
              Mock API URL: <Text code>http://127.0.0.1:4523/m1/345161-308456-default</Text>
            </Text>
            <br />
            <Text type="warning" style={{ fontSize: 12 }}>
              Note: Toggling this switch only updates the UI. To actually change the mode, use npm scripts or restart
              the app.
            </Text>
          </div>
        )}

        <Alert
          message="Mock API Availability"
          description={
            <div>
              <Text>Mock API is only available in the following environments:</Text>
              <ul style={{ marginTop: 4, marginBottom: 0 }}>
                <li>
                  <Tag color="green">Development</Tag>
                </li>
                <li>
                  <Tag color="blue">Test</Tag>
                </li>
              </ul>
              <Text type="secondary">
                Not available in: <Tag color="orange">Gray</Tag> <Tag color="red">Production</Tag>
              </Text>
            </div>
          }
          type="info"
          showIcon
          style={{ marginBottom: 8 }}
        />
      </Space>

      <Divider style={{ margin: '8px 0' }} />

      <div>
        <Text type="secondary">
          Current environment: <Text code>{currentEnv}</Text>
        </Text>
        <br />
        <Text type="secondary">
          Raw environment value: <Text code>{rawEnv}</Text>
        </Text>
        <br />
        <Space direction="vertical" style={{ marginTop: 8 }}>
          <Text type="secondary">Configurations:</Text>
          <Space>
            <Tag color="blue">Menu: Development</Tag>
            <Tag color="green">Notice: Development</Tag>
            {useMockApi && isMockAllowed() && <Tag color="orange">API: Mock</Tag>}
          </Space>
        </Space>
      </div>
    </Card>
  );
};

export default DevTools;
