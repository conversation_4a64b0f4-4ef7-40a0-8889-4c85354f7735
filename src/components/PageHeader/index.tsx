/**
 * @Owners ljh
 * @Title 页面头部
 */
import './index.less';

import { LeftOutlined } from '@ant-design/icons';
import { Button, Flex } from 'antd';
import { type ReactNode } from 'react';

export interface PageHeaderProps {
  /** 自定义 back icon ，如果为 false 不渲染 back icon */
  backIcon?: ReactNode | boolean;
  /** 操作区，位于 title 行的行尾 */
  extra?: ReactNode;
  /** 自定义标题文字 */
  title?: ReactNode;
  /** 返回按钮的点击事件 */
  onBack?: () => void;
}

const PageHeader = (props: PageHeaderProps) => {
  const { title, onBack, backIcon = <LeftOutlined />, extra } = props;

  return (
    <Flex className="page-header" justify="space-between" align="center">
      <Button className="page-header-back" icon={backIcon} size="large" color="default" variant="link" onClick={onBack}>
        {title}
      </Button>
      {extra}
    </Flex>
  );
};

export default PageHeader;
