/**
 * @Owners ljh
 * @Title 搜索查询过滤器
 */
import './index.less';

import { UpOutlined } from '@ant-design/icons';
import { Button, type ButtonProps, Col, Form, Row, Space } from 'antd';
import classNames from 'classnames';
import {
  forwardRef,
  type ReactNode,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';

import { useLocale } from '@/locales';

import Show from '../Show';

export interface SearchQueryFilterColumn {
  /** 一个表单项占用的格子数量，占比=colSize*span，span 默认是8 */
  colSize?: number;
  /** 表单项的名称，会作为表单的 key */
  name: string;
  /** 表单项的标题 */
  title?: ReactNode;
  /** 会在 title 之后展示一个 icon，hover 之后提示一些信息 */
  tooltip?: string;
  render?: () => ReactNode;
}

export interface SearchQueryFilterProps {
  columns?: SearchQueryFilterColumn[];
  /** 是否自动缩略 */
  ellipsis?: boolean;
  /** 重置按钮的 props */
  resetBtnProps?: ButtonProps;
  /** 搜索按钮的 props */
  searchBtnProps?: ButtonProps;
  /** 重置按钮的回调函数 */
  onReset?: () => void;
  /** 搜索按钮的回调函数 */
  onSearch?: (values: any) => void;
}

export interface SearchQueryFilterRef {
  /** 获取搜索的一组字段名对应的值 */
  getSearchFieldsValue: () => any;
}

/** 默认 col span配置 */
const DEFAULT_COL_SPAN = {
  xs: 8,
  xxl: 6,
};

const SearchQueryFilter = forwardRef<SearchQueryFilterRef, SearchQueryFilterProps>((props, ref) => {
  const { columns, ellipsis = true, resetBtnProps, searchBtnProps, onSearch, onReset } = props;
  const { formatMessage } = useLocale();

  const [form] = Form.useForm();
  /** 当前的 col span */
  const [colSpan, setColSpan] = useState(8);
  /** 是否展开 */
  const [isCollapsed, setIsCollapsed] = useState(ellipsis);
  const searchValues = useRef<any>({});

  useImperativeHandle(ref, () => {
    return {
      getSearchFieldsValue: () => {
        return searchValues.current;
      },
    };
  });

  /** 总共占用的 span */
  const totalSpan = useMemo(() => {
    return columns?.reduce((acc, cur) => acc + (cur.colSize || 1) * colSpan, colSpan) || 0;
  }, [colSpan, columns]);

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;

      if (width >= 1600) {
        setColSpan(DEFAULT_COL_SPAN.xxl);
      } else {
        setColSpan(DEFAULT_COL_SPAN.xs);
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize();

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    setIsCollapsed(ellipsis);
  }, [ellipsis]);

  /** 重置 */
  const handleReset = () => {
    searchValues.current = {};
    onReset?.();
  };

  /** 搜索 */
  const handleSearch = (values: any) => {
    searchValues.current = values;
    onSearch?.(values);
  };

  /**
   * 收起/展开表单
   */
  const handleToggleCollapse = useCallback(() => {
    setIsCollapsed(ic => !ic);
  }, []);

  /**
   * 渲染操作按钮
   */
  const renderActions = useMemo(() => {
    return (
      <Space>
        <Button htmlType="reset" {...resetBtnProps}>
          {formatMessage({ id: 'global.tips.reset' })}
        </Button>
        <Button type="primary" htmlType="submit" {...searchBtnProps}>
          {formatMessage({ id: 'global.tips.search' })}
        </Button>

        <Show when={ellipsis && totalSpan > 24}>
          <Button
            className={classNames('query-filter-collapse-btn', {
              'query-filter-collapse-btn--collapsed': isCollapsed,
            })}
            type="link"
            onClick={handleToggleCollapse}
          >
            {isCollapsed ? formatMessage({ id: 'global.tips.expand' }) : formatMessage({ id: 'global.tips.collapse' })}
            <UpOutlined />
          </Button>
        </Show>
      </Space>
    );
  }, [handleToggleCollapse, resetBtnProps, searchBtnProps, isCollapsed, totalSpan, ellipsis]);

  /**
   * 渲染表单
   */
  const renderFormColumns = useMemo(() => {
    /** 每行已占用的 span */
    let rowSpan = 0;
    let isHidden = false;

    const formColumnNodes =
      columns?.map(column => {
        const { colSize = 1, name, title, tooltip, render } = column;
        const span = colSize * colSpan;

        const tempRowSpan = rowSpan + span;

        if (!isCollapsed) {
          // 展开状态，每行 span 都要对 24 取余
          // remark 大于 24 会独立换行，因此 当前行 span 就是这个元素的 span
          rowSpan = tempRowSpan > 24 ? span : tempRowSpan % 24;
        } else {
          // 收起状态，只有一行
          if (!isHidden) {
            // 24 - colSpan 表示 actions 必须要放最后，因此要减去 actions 占用的 colSpan
            isHidden = tempRowSpan > 24 - colSpan;
            rowSpan = isHidden ? rowSpan : tempRowSpan;
          }
        }

        return (
          <Col
            span={span}
            key={name}
            className={classNames({
              'query-filter-col-hidden': isHidden,
            })}
          >
            <Form.Item
              label={title}
              tooltip={tooltip}
              name={name}
              labelCol={!!title ? { flex: '0 0 120px' } : undefined}
              wrapperCol={!!title ? { style: { maxWidth: 'calc(100% - 120px)' } } : undefined}
            >
              {render?.()}
            </Form.Item>
          </Col>
        );
      }) || [];

    formColumnNodes.push(
      <Col key="actions" span={colSpan} offset={24 - rowSpan - colSpan} style={{ textAlign: 'end' }}>
        <Form.Item>{renderActions}</Form.Item>
      </Col>,
    );

    return formColumnNodes;
  }, [colSpan, isCollapsed, columns, renderActions]);

  return (
    <div className="search-query-filter">
      <Form form={form} className="query-filter" onReset={handleReset} onFinish={handleSearch}>
        <Row gutter={[24, 24]}>{renderFormColumns}</Row>
      </Form>
    </div>
  );
});

export default SearchQueryFilter;
