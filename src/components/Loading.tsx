/**
 * @author: limengyi
 * @description: Loading组件
 */

import type { SpinSize } from 'antd/es/spin';
import type { FC } from 'react';

import { Spin } from 'antd';

interface LoadingProps {
  size?: SpinSize;
  center?: boolean;
}
const Loading: FC<LoadingProps> = ({ size = 'default', center = false }: LoadingProps) => (
  <div className={`${center ? 'common-loading-wrapper' : ''}`}>
    <Spin size={size} />
  </div>
);

export default Loading;
