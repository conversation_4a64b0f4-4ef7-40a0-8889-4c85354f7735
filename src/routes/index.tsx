import type { AppRouteObject as RouteType } from './types';
import type { FC } from 'react';

import { lazy } from 'react';
import { Navigate } from 'react-router';
import { useRoutes } from 'react-router-dom';

import LayoutPage from '@/pages/layout';
import LoginPage from '@/pages/login';

const Dashboard = lazy(() => import(/* webpackChunkName: "dashboard" */ '@/pages/dashboard'));
const PersonalCustomerList = lazy(
  () => import(/* webpackChunkName: "personal-customer-list" */ '@/pages/personalCustomerList'),
);
const PersonalCustomerDetail = lazy(
  () => import(/* webpackChunkName: "personal-customer-detail" */ '@/pages/personalCustomerDetail'),
);
const ProductList = lazy(() => import(/* webpackChunkName: "product-list" */ '@/pages/product/list'));
const ProductDetail = lazy(() => import(/* webpackChunkName: "product-detail" */ '@/pages/product/detail'));
const ProductCreate = lazy(() => import(/* webpackChunkName: "product-create" */ '@/pages/product/create'));

const NotFound = lazy(() => import(/* webpackChunkName: "404" */ '@/pages/404'));

export const routeList: RouteType[] = [
  {
    path: '/login',
    element: <LoginPage />,
  },
  {
    path: '/',
    element: <LayoutPage />,
    children: [
      {
        path: '',
        element: <Navigate to="customer/personal" />,
      },
      {
        /** 个人客户 */
        path: 'customer/personal',
        element: <PersonalCustomerList />,
      },
      {
        /** 个人客户详情 */
        path: 'customer/personal/:customerId',
        element: <PersonalCustomerDetail />,
        parentPath: '/customer/personal',
      },
      {
        /** 产品信息 */
        path: 'products',
        element: <ProductList />,
      },
      {
        /** 新建产品 */
        path: 'products/create',
        element: <ProductCreate />,
        parentPath: '/products',
      },
      {
        /** 产品信息详情 */
        path: 'products/:productId',
        element: <ProductDetail />,
        parentPath: '/products',
      },
      {
        /** 产品公告 */
        path: 'product/announcement',
        /** TODO(LIMENGYI): 替换真正的组件 */
        element: <NotFound />,
      },
      {
        /** 认购申请 */
        path: 'order/subscribe',
        /** TODO(LIMENGYI): 替换真正的组件 */
        element: <NotFound />,
      },
      {
        /** 份额查询 */
        path: 'order/query',
        /** TODO(LIMENGYI): 替换真正的组件 */
        element: <NotFound />,
      },
      {
        /** 404 */
        path: '*',
        /** TODO(LIMENGYI): 替换真正的组件 */
        element: <NotFound />,
      },
    ],
  },
];

const RenderRouter: FC = () => {
  const element = useRoutes(routeList);

  return element;
};

export default RenderRouter;
