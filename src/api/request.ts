import type { AxiosRequestConfig, Method } from 'axios';

import { message as $message } from 'antd';
import axios from 'axios';

import { baseApi, currentEnv, EnvType, testApi } from '@/config/env';
import store from '@/stores';
import { setGlobalState } from '@/stores/global.store';

// Allow switching to test API in development mode
// Set this to true to use test API in development environment
let useTestApiInDev = false;

// Mock API URL
const MOCK_API_URL = 'http://127.0.0.1:4523/m1/345161-308456-default';

// Check if mock mode is enabled via environment variable
const isMockModeEnabled = import.meta.env.VITE_USE_MOCK_API === 'true';

// Extend AxiosRequestConfig to include mock option
export interface RequestConfig extends AxiosRequestConfig {
  mock?: boolean;
}

// Check if current environment allows mock API
const isMockAllowed = () => {
  console.log('currentEnv', currentEnv);

  // Only allow mock API in development and test environments
  return currentEnv === EnvType.Development;
};

// Get the appropriate API URL based on environment and settings
const getApiUrl = (useMock?: boolean) => {
  // If mock is explicitly requested and environment allows it, use mock API
  if ((useMock || isMockModeEnabled) && isMockAllowed()) {
    return MOCK_API_URL;
  }

  if (currentEnv === EnvType.Development && useTestApiInDev && testApi) {
    return testApi;
  }

  return baseApi;
};

const axiosInstance = axios.create({
  timeout: 6000,
  baseURL: getApiUrl(),
});

// Log current environment
console.log(`Current environment: ${currentEnv}`);

if (currentEnv === EnvType.Development) {
  console.log(`Using API: ${useTestApiInDev ? 'Test API' : 'Local API'}`);

  if (isMockModeEnabled) {
    console.log(`Mock API mode is enabled: ${MOCK_API_URL}`);
  }
}

// Function to switch between local and test APIs in development
export const switchToTestApi = (useTest: boolean) => {
  if (currentEnv !== EnvType.Development) {
    console.warn('Cannot switch API in non-development environment');

    return;
  }

  useTestApiInDev = useTest;
  axiosInstance.defaults.baseURL = getApiUrl();
  console.log(`Switched to ${useTestApiInDev ? 'Test API' : 'Local API'}: ${axiosInstance.defaults.baseURL}`);
};

axiosInstance.interceptors.request.use(
  config => {
    store.dispatch(
      setGlobalState({
        loading: true,
      }),
    );
    console.log('config', config);

    // Check if this request should use mock API
    const customConfig = config as RequestConfig;

    console.log('111', customConfig.mock);

    if (customConfig.mock && isMockAllowed()) {
      config.baseURL = MOCK_API_URL;
      console.log(`Using mock API for request: ${config.url}`);
    } else if (customConfig.mock && !isMockAllowed()) {
      console.warn(`Mock API is not allowed in ${currentEnv} environment. Using real API instead.`);
    }

    // Add environment-specific headers or logic if needed
    if (currentEnv !== EnvType.Production) {
      console.log(`Making API request to: ${config.baseURL}${config.url}`);
    }

    console.log('config', config);

    return config;
  },
  error => {
    store.dispatch(
      setGlobalState({
        loading: false,
      }),
    );
    Promise.reject(error);
  },
);

axiosInstance.interceptors.response.use(
  config => {
    store.dispatch(
      setGlobalState({
        loading: false,
      }),
    );

    if (config?.data?.message) {
      // $message.success(config.data.message)
    }

    return config?.data;
  },
  error => {
    store.dispatch(
      setGlobalState({
        loading: false,
      }),
    );

    /* TODO(LIMENGYI): 确认登录过期的状态码，跳转回登录页 */
    if (error?.response?.status === 401) {
      window.location.href = `${window.location.origin}/login`;
      $message.error('登录已过期，请重新登录');

      return;
    }

    let errorMessage = '系统异常';

    if (error?.message?.includes('Network Error')) {
      errorMessage = '网络错误，请检查您的网络';
    } else {
      errorMessage = error?.message;
    }

    console.dir(error);
    error.message && $message.error(errorMessage);

    return {
      status: false,
      message: errorMessage,
      result: null,
    };
  },
);

export type Response<T = any> = {
  status: boolean;
  message: string;
  result: T;
};

export type MyResponse<T = any> = Promise<Response<T>>;

/**
 *
 * @param method - request methods
 * @param url - request url
 * @param data - request data or params
 * @param config - axios config, can include mock: true to use mock API
 */
export const request = <T = any>(
  method: Lowercase<Method>,
  url: string,
  data?: any,
  config?: RequestConfig,
): MyResponse<T> => {
  // No need for prefix as we're using baseURL in axios instance
  // const prefix = '/api'
  // const prefix = '';
  // url = prefix + url;

  console.log('data', data);

  if (method === 'post') {
    return axiosInstance.post(url, data, config);
  } else {
    return axiosInstance.get(url, {
      params: data,
      ...config,
    });
  }
};
