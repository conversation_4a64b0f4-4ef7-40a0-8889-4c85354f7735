import type { PaginationParams } from '@/types';
import type { CustomerData, CustomerDetailData, SearchParams } from '@/types/customer';

import { request } from './request';

/** 获取个人客户列表接口 */
export const apiGetCustomerList = (data: PaginationParams & SearchParams) =>
  request<CustomerData[]>('get', '/customer/list', data, {
    mock: true,
  });

/** 获取个人客户详情接口 */
export const apiGetCustomerDetail = (customerId: string) =>
  request<{ data: CustomerDetailData }>(
    'get',
    `/customer/detail/${customerId}`,
    {},
    {
      mock: true,
    },
  );
