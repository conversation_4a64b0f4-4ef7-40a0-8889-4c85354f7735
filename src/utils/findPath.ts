import type { AppRouteObject } from '@/routes/types';

import { routeList } from '@/routes/index';

/**
 * 获取一个字符串值在指定字符串第n次出现的位置
 */
export function getStrTimesIndex(str: string, cha: string, num: number) {
  let x = str.indexOf(cha);

  for (let i = 0; i < num; i++) {
    x = str.indexOf(cha, x + 1);
  }

  return x;
}

export function getFirstPathCode(path: string) {
  const index0 = getStrTimesIndex(path, '/', 0);
  const index1 = getStrTimesIndex(path, '/', 1);

  const activeKey = path.slice(index0 + 1, index1 > 0 ? index1 : path.length);

  return activeKey;
}

/**
 * 将路径字符串转换为正则表达式
 * @param path 路径字符串
 * @returns 正则表达式对象
 * @example 将 /product/info/:productId 转为 /^\/product\/info\/[^/]+\/?$/
 */
function pathToRegexp(path: string) {
  //
  const regex = path
    .replace(/:[^/]+/g, '[^/]+') // :param -> [^/]+
    .replace(/\//g, '\\/'); // / -> \/

  return new RegExp('^' + regex + '\\/?$');
}

/**
 * 根据路径查找路由
 * @param pathname 路径URL
 * @param routes 路由列表
 * @returns 找到的路由或 null
 */
export function findRouteByPath(pathname: string, routes = routeList): AppRouteObject | null {
  if (pathname === '/') {
    return null;
  }

  for (const route of routes) {
    if (route.path && pathToRegexp(route.path).test(pathname.replace(/^\//, ''))) {
      return route;
    }

    if (route.children) {
      const found = findRouteByPath(pathname, route.children);

      if (found) return found;
    }
  }

  return null;
}
