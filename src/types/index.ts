import { LocaleFormatter } from '@/locales';

export interface Locales<T = any> {
  /** Chinese */
  zh_CN: T;
  /** English */
  en_US: T;
}

export type Language = keyof Locales;

export interface PageData<T> {
  pageNum: number;
  pageSize: number;
  total: number;
  data: T[];
}

export interface PaginationParams {
  current: number;
  pageSize: number;
}

/** 审核状态枚举 */
export enum AuditStatus {
  /** 未审核 */
  UNCHECKED = 0,
  /** 审核通过 */
  PASSED = 1,
  /** 审核不通过 */
  REJECTED = 2,
}

/** 审核状态多语言映射 */
export const AuditStatusLanguageMap: Record<AuditStatus, React.ReactElement | null> = {
  [AuditStatus.PASSED]: LocaleFormatter({ id: 'global.auditStatus.approved' }),
  [AuditStatus.REJECTED]: LocaleFormatter({ id: 'global.auditStatus.rejected' }),
  [AuditStatus.UNCHECKED]: LocaleFormatter({ id: 'global.auditStatus.pending' }),
};
