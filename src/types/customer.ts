import type { AuditStatus } from '.';

import { LocaleFormatter } from '@/locales';

/** 身份证件类型 */
export enum CertificateType {
  /** 大陆身份证 */
  ID_CARD = 0,
  /** 香港身份证 */
  HONG_KONG_ID_CARD = 1,
  /** 护照 */
  PASSPORT = 2,
}

/** 客户风险类别 */
export enum RiskCategory {
  /** 保守 */
  CONSERVATIVE = 0,
  /** 中度保守 */
  MODERATE_CONSERVATIVE = 1,
  /** 平稳 */
  BALANCED = 2,
  /** 中度进取 */
  MODERATE_AGGRESSIVE = 3,
  /** 进取 */
  AGGRESSIVE = 4,
}

/** 风险尺度 */
export enum RiskScale {
  /** 极低风险 */
  VERY_LOW = 0,
  /** 低至中度风险 */
  LOW_TO_MEDIUM = 1,
  /** 中风险 */
  MEDIUM = 2,
  /** 中至高风险 */
  MEDIUM_TO_HIGH = 3,
  /** 高风险 */
  HIGH = 4,
}

/** 身份证件类型多语言映射 */
export const CertificateTypeLanguageMap: Record<CertificateType, React.ReactElement | null> = {
  [CertificateType.ID_CARD]: LocaleFormatter({ id: 'personalCustomer.certificateType.idCard' }),
  [CertificateType.HONG_KONG_ID_CARD]: LocaleFormatter({
    id: 'personalCustomer.certificateType.hkIdCard',
  }),
  [CertificateType.PASSPORT]: LocaleFormatter({ id: 'personalCustomer.certificateType.passport' }),
};

/** 客户风险类别多语言映射 */
export const RiskResultMap: Record<RiskCategory, React.ReactElement | null> = {
  [RiskCategory.CONSERVATIVE]: LocaleFormatter({ id: 'personalCustomer.riskResult.conservative' }),
  [RiskCategory.MODERATE_CONSERVATIVE]: LocaleFormatter({
    id: 'personalCustomer.riskResult.moderateConservative',
  }),
  [RiskCategory.BALANCED]: LocaleFormatter({ id: 'personalCustomer.riskResult.balanced' }),
  [RiskCategory.MODERATE_AGGRESSIVE]: LocaleFormatter({
    id: 'personalCustomer.riskResult.moderateAggressive',
  }),
  [RiskCategory.AGGRESSIVE]: LocaleFormatter({ id: 'personalCustomer.riskResult.aggressive' }),
};

export interface CustomerData {
  key: string;
  customerNumber: string;
  investorName: string;
  certificateType: CertificateType;
  certificateNumber: string;
  registrationTime: number;
  riskAssessment: {
    dueDate: number;
    result: RiskCategory;
  };
  identityVerification: {
    uploadDate: number;
    auditStatus: AuditStatus;
  };
  currentAddressVerification: {
    uploadDate: number;
    auditStatus: AuditStatus;
  };
  assetVerification: {
    uploadDate: number;
    auditStatus: AuditStatus;
  };
  notes: string;
}

export interface SearchParams {
  loginAccount?: string;
  investorName?: string;
  certificateNumber?: string;
  identityStatus?: AuditStatus;
  addressStatus?: AuditStatus;
  assetStatus?: AuditStatus;
}

export interface CustomerDetailData {
  basicInfo: {
    investorName: string;
    loginAccount: string;
    registrationTime: number;
    certificateType: string;
    certificateNumber: string;
    certificateExpiry: number;
    gender: string;
    birthDate: number;
    country: string;
  };
  riskAssessment: {
    assessmentDate: number;
    assessmentResult: RiskCategory;
    assessmentExpiry: number;
    score: number;
  };
  residenceInfo: {
    verificationDate: number;
    residenceAddress: string;
  };
}
