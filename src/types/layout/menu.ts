interface MenuItem {
  /**
   *  menu item key(控制菜单选中和展开状态)
   * 父级的key是子菜单的前缀
   * 例如：'/customer/personal' 的 key 是 'customer'
   *
   * 子集的key是完整的路径，同path
   * 例如：'/customer/personal' 的 key 是 '/customer/personal'
   */
  key: string;
  /** menu 显示名称 */
  label: string;
  /** 图标名称 */
  icon?: string;
  /** 菜单路由 */
  path: string;
  /** 子菜单 */
  children?: MenuItem[];
}

export type MenuChild = Omit<MenuItem, 'children'>;

export type MenuList = MenuItem[];
