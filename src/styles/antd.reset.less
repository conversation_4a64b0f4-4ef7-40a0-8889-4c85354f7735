.ant-list-item {
  padding: 12px 12px !important;
}

.ant-table-cell .ant-btn.ant-btn-link {
  padding: 0 4px !important;
}

.ant-btn + .ant-btn {
  margin-left: 10px;
}

.search-form {
  .ant-form-item {
    // margin-bottom: 16px;
    
    .ant-form-item-label {
      white-space: nowrap;
      min-width: 80px;
    }
  }
  
  // 响应式布局
  @media (max-width: 1200px) {
    .ant-col {
      flex: 0 0 50% !important;
      max-width: 50% !important;
    }
  }
  
  @media (max-width: 768px) {
    .ant-col {
      flex: 0 0 100% !important;
      max-width: 100% !important;
    }
    
    .ant-form-item {
      .ant-form-item-label {
        text-align: left;
      }
    }
  }
  
  @media (max-width: 576px) {
    .ant-form {
      .ant-form-item {
        .ant-form-item-label {
          padding-bottom: 4px;
        }
      }
    }
  }
}

.ant-table {
  // 表头样式优化
  .ant-table-thead > tr > th {
    background-color: #fafafa;
    font-weight: 600;
    border-right: 1px solid #d9d9d9; // 加强表头右边框
    position: relative;
    text-align: center;
    padding: 12px 8px;
    white-space: nowrap;

    // 最后一列不需要右边框
    &:last-child {
      border-right: none;
    }
    
    // 多级表头父级样式
    &.ant-table-cell-with-append {
      border-bottom: 2px solid #bfbfbf; // 父级表头加粗下边框
    }
  }

  // 表头分组优化
  .ant-table-thead {
    // 多级表头第一行样式
    > tr:first-child > th {
      background: linear-gradient(to bottom, #fafafa 0%, #f0f0f0 100%);
      font-weight: 700;
      font-size: 14px;
      color: #262626;
      text-align: center;
      border-bottom: 2px solid #d9d9d9;
      
      // 多级表头分组边框加强
      &.ant-table-cell-with-append {
        border-right: 2px solid #bfbfbf;
        
        &:last-child {
          border-right: none;
        }
      }
    }

    // 多级表头第二行样式
    > tr:last-child > th {
      background-color: #f8f8f8;
      font-weight: 600;
      font-size: 13px;
      color: #595959;

      // 子表头分割线
      &:not(:last-child) {
        border-right: 1px solid #d9d9d9;
      }
    }
  }

  // 表格整体圆角和阴影
  .ant-table-container {
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    
    // 表头圆角
    .ant-table-thead > tr:first-child > th {
      &:first-child {
        border-top-left-radius: 6px;
      }
      &:last-child {
        border-top-right-radius: 6px;
      }
    }
  }

  // 表体样式
  .ant-table-tbody > tr > td {
    border-bottom: 1px solid #f0f0f0;
    border-right: 1px solid #f5f5f5; // 添加列分割线
    
    // 数字列右对齐
    &[data-align="right"] {
      text-align: right;
    }
    
    // 状态列居中
    &[data-align="center"] {
      text-align: center;
    }
    
    // 内容间距优化
    padding: 12px 8px;
    line-height: 1.5;
    
    // 链接按钮样式
    .ant-btn-link {
      padding: 0;
      height: auto;
      border: none;
      box-shadow: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
    
    // 最后一列不需要右边框
    &:last-child {
      border-right: none;
    }
  }

  .ant-table-tbody > tr:hover > td {
    background-color: #f5f5f5;
  }

  // 固定列样式优化
  .ant-table-cell-fix-left,
  .ant-table-cell-fix-right {
    background-color: #fafafa;
  }
  
  // 滚动阴影效果
  .ant-table-ping-left .ant-table-cell-fix-left-last::after {
    box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, 0.15);
  }
  
  .ant-table-ping-right .ant-table-cell-fix-right-first::after {
    box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, 0.15);
  }
}

// 空数据状态
.ant-empty {
  margin: 40px 0;
  
  .ant-empty-description {
    color: #8c8c8c;
  }
}

.ant-pagination {
  margin-top: 16px;
  text-align: right;
}

.tabs-container {
  .ant-tabs-nav {
    margin-bottom: 24px;
    
    &::before {
      border-bottom: 1px solid #f0f0f0;
    }
    
    .ant-tabs-tab {
      padding: 12px 0;
      font-size: 14px;
      
      &.ant-tabs-tab-active .ant-tabs-tab-btn {
        font-weight: 500;
      }
    }
  }
}