const zh_CN = {
  'global.title': 'POLARIS CAPITAL 后台管理系统',
  'global.tips.notfound': '对不起，您访问的页面不存在。',
  'global.tips.unauthorized': '对不起，您没有权限访问此页。',
  'global.tips.goToLogin': '去登录',
  'global.tips.backHome': '返回首页',
  'global.tips.operation': '操作',
  'global.tips.authorize': '授权',
  'global.tips.delete': '删除',
  'global.tips.create': '新建',
  'global.tips.modify': '修改',
  'global.tips.search': '搜索',
  'global.tips.reset': '重置',
  'global.tips.expand': '展开',
  'global.tips.collapse': '收起',
  'global.tips.deleteConfirm': '确定要删除此条数据吗？',
  'global.tips.loading': '加载中...',
  'global.login': '登录',
  'global.logout': '登出',
  'global.title.notFound': '404',
  'global.auditStatus.pending': '未审核',
  'global.auditStatus.approved': '审核通过',
  'global.auditStatus.rejected': '审核不通过',
  'global.year': '年',
  'global.placeholder.inputPrefix': '请输入',
  'global.placeholder.selectPrefix': '请选择',

  // 侧边导航栏
  'menu.customerManage': '客户管理',
  'menu.personalCustomer': '个人客户',
  'menu.productManage': '产品管理',
  'menu.productInfo': '产品信息',
  'menu.productAnnouncement': '产品公告',
  'menu.orderManage': '订单管理',
  'menu.subscribeApplication': '认购申请',
  'menu.quotaQuery': '份额查询',

  // 登录页面
  'login.title': '欢迎登录 {name} 平台',
  'login.button': '登录',
  'login.username': '用户名',
  'login.enterUsernameMessage': '请输入用户名！',
  'login.password': '密码',
  'login.enterPasswordMessage': '请输入密码！',
  'login.rememberUser': '记住用户',
  'login.error': '登录失败，请检查用户名和密码是否正确！',

  // 个人客户列表页面
  'personalCustomer.table.customerNumber': '登录账号',
  'personalCustomer.table.investorName': '投资者姓名',
  'personalCustomer.table.certificateType': '证件类型',
  'personalCustomer.table.certificateNumber': '证件号码',
  'personalCustomer.table.registrationTime': '注册时间',
  'personalCustomer.table.riskAssessment': '风险问卷',
  'personalCustomer.table.riskAssessment.dueDate': '问卷日期',
  'personalCustomer.table.riskAssessment.result': '问卷结果',
  'personalCustomer.table.identityVerification': '身份证明',
  'personalCustomer.table.identityVerification.uploadDate': '上传日期',
  'personalCustomer.table.identityVerification.auditStatus': '审核状态',
  'personalCustomer.table.currentAddressVerification': '现居地址证明',
  'personalCustomer.table.currentAddressVerification.uploadDate': '上传日期',
  'personalCustomer.table.currentAddressVerification.auditStatus': '审核状态',
  'personalCustomer.table.assetVerification': '资产证明',
  'personalCustomer.table.assetVerification.uploadDate': '上传日期',
  'personalCustomer.table.assetVerification.auditStatus': '审核状态',
  'personalCustomer.table.notes': '备注',
  'personalCustomer.table.viewDetails': '查看详情',
  'personalCustomer.search.loginAccount': '登录账号',
  'personalCustomer.search.investorName': '投资者姓名',
  'personalCustomer.search.certificateNumber': '证件号码',
  'personalCustomer.search.identityStatus': '身份审核状态',
  'personalCustomer.search.addressStatus': '地址审核状态',
  'personalCustomer.search.assetStatus': '资产证明审核',
  'personalCustomer.search.placeholder.input': '请输入',
  'personalCustomer.search.placeholder.select': '请选择审核状态',
  'personalCustomer.search.button.query': '查询',
  'personalCustomer.pagination.total': '共{total}条数据',
  'personalCustomer.certificateType.idCard': '大陆身份证',
  'personalCustomer.certificateType.hkIdCard': '香港身份证',
  'personalCustomer.certificateType.passport': '护照',
  'personalCustomer.comment.searchReset': '搜索时重置到第一页',
  'personalCustomer.comment.pageSizeReset': '改变页面大小时重置到第一页',
  'personalCustomer.error.fetchFailed': '获取数据失败:',
  'personalCustomer.riskResult.conservative': '保守型',
  'personalCustomer.riskResult.moderateConservative': '中度保守型',
  'personalCustomer.riskResult.balanced': '平衡型',
  'personalCustomer.riskResult.moderateAggressive': '中度积极型',
  'personalCustomer.riskResult.aggressive': '积极型',

  // 个人客户详情页
  'personalCustomerDetail.title': '客户详情',
  'personalCustomerDetail.tabs.basicInfo': '基本信息',
  'personalCustomerDetail.tabs.riskAssessment': '风险问卷',
  'personalCustomerDetail.tabs.identityVerification': '身份证明',
  'personalCustomerDetail.tabs.residenceVerification': '地址证明',
  'personalCustomerDetail.tabs.assetVerification': '资产证明',
  'personalCustomerDetail.basicInfo.loginAccount': '登录账号',
  'personalCustomerDetail.basicInfo.registrationTime': '注册时间',
  'personalCustomerDetail.basicInfo.certificateType': '证件类型',
  'personalCustomerDetail.basicInfo.certificateNumber': '证件号码',
  'personalCustomerDetail.basicInfo.certificateExpiry': '证件有效期',
  'personalCustomerDetail.basicInfo.gender': '性别',
  'personalCustomerDetail.basicInfo.birthDate': '出生日期',
  'personalCustomerDetail.basicInfo.country': '国籍',
  'personalCustomerDetail.riskAssessment.assessmentDate': '风险问卷日期',
  'personalCustomerDetail.riskAssessment.assessmentResult': '问卷结果',
  'personalCustomerDetail.riskAssessment.assessmentExpiry': '问卷有效期',
  'personalCustomerDetail.riskAssessment.score': '问卷得分',
  'personalCustomerDetail.riskAssessment.date': '问卷日期',
  'personalCustomerDetail.residenceInfo.verificationDate': '现居地址CTC认证日期',
  'personalCustomerDetail.residenceInfo.residenceAddress': '现居地址',

  // 风险问卷
  'riskSurvey.section1': '个人及财务资料',
  'riskSurvey.educationLevel': '1.1 您的教育程度为?',
  'riskSurvey.educationLevel.primary': '小学或以下*',
  'riskSurvey.educationLevel.secondary': '中学',
  'riskSurvey.educationLevel.postSecondary': '大专或专上',
  'riskSurvey.educationLevel.university': '大学',
  'riskSurvey.educationLevel.additional': '*另请填妥「需要特别照顾客户声明」',
  'riskSurvey.annualIncome': '1.2 您每年收入为?',
  'riskSurvey.annualIncome.1': '≤HK$30',
  'riskSurvey.annualIncome.2': 'HK$30万-80万',
  'riskSurvey.annualIncome.3': 'HK$80万-120万',
  'riskSurvey.annualIncome.4': 'HK$120万以上，请注明',
  'riskSurvey.annualIncome.5': 'HK$0，原因',
  'riskSurvey.annualIncome.placeholder': '请注明',
  'riskSurvey.annualIncome.reason': '原因',
  'riskSurvey.netWorth': '1.3 您的资产净值约为?',
  'riskSurvey.netWorth.1': '≤HK$1百万',
  'riskSurvey.netWorth.2': 'HK$1百万-4百万',
  'riskSurvey.netWorth.3': 'HK$4百万-8百万',
  'riskSurvey.netWorth.4': 'HK$8百万以上，请说明',
  'riskSurvey.assetType': '资产类型',
  'riskSurvey.assetType.property': '物业',
  'riskSurvey.assetType.cash': '现金',
  'riskSurvey.assetType.stock': '股票',
  'riskSurvey.assetType.others': '其他',
  'riskSurvey.sourceOfFunds': '2. 资金来源',
  'riskSurvey.sourceOfFunds.salary': '工资/佣金',
  'riskSurvey.sourceOfFunds.business': '个人业务',
  'riskSurvey.sourceOfFunds.saleOfRealEstate': '售出物业',
  'riskSurvey.sourceOfFunds.savings': '储蓄',
  'riskSurvey.sourceOfFunds.others': '其他',
  'riskSurvey.sourceOfFunds.investmentIncome': '投资获利',
  'riskSurvey.sourceOfFunds.investmentSource': '投资获利来源',
  'riskSurvey.sourceOfFunds.stock': '股票',
  'riskSurvey.sourceOfFunds.bonds': '债券',
  'riskSurvey.sourceOfWealth': '3. 财产来源',
  'riskSurvey.sourceOfWealth.salary': '工资/退休金',
  'riskSurvey.sourceOfWealth.business': '个人业务收益',
  'riskSurvey.sourceOfWealth.inheritance': '遗产继承/赠与',
  'riskSurvey.sourceOfWealth.others': '其他',
  'riskSurvey.sourceOfWealth.investment': '投资获利',
  'riskSurvey.anticipatedLevelOfActivity': '4. 预计每年交易总额(港币)',
  'riskSurvey.anticipatedLevelOfActivity.1': '≤HK$1百万',
  'riskSurvey.anticipatedLevelOfActivity.2': 'HK$1百万-5百万',
  'riskSurvey.anticipatedLevelOfActivity.3': 'HK$5百万-10百万',
  'riskSurvey.anticipatedLevelOfActivity.4': 'HK$10百万以上，请说明',
  'riskSurvey.riskProfiling': '5. 风险评估问卷',
  'riskSurvey.longestInvestmentPeriod': '5.1 最长投资年期',
  'riskSurvey.longestInvestmentPeriod.1': '少于1年',
  'riskSurvey.longestInvestmentPeriod.2': '1年至2年',
  'riskSurvey.longestInvestmentPeriod.3': '2年至4年',
  'riskSurvey.longestInvestmentPeriod.4': '4年至7年',
  'riskSurvey.longestInvestmentPeriod.5': '7年以上',
  'riskSurvey.investmentGoal': '5.2 以下哪一个投资目标最能表达您的投资目标?',
  'riskSurvey.investmentGoal.1': '保本，即使回报很低',
  'riskSurvey.investmentGoal.2': '追求高于银行存款的稳定收入',
  'riskSurvey.investmentGoal.3': '追求稳定收入和资本增值',
  'riskSurvey.investmentGoal.4': '追求长期资本增值',
  'riskSurvey.investmentGoal.5': '追求高资本增值',
  'riskSurvey.dropResponse': '5.3 如果您投资组合中风险最高的投资价值下跌，您会如何反应?',
  'riskSurvey.dropResponse.1': '立即全部卖出',
  'riskSurvey.dropResponse.2': '卖出部分，减少风险',
  'riskSurvey.dropResponse.3': '维持投资，除非基本面改变',
  'riskSurvey.dropResponse.4': '维持投资，无论损失多大，除非基本面改变',
  'riskSurvey.dropResponse.5': '维持投资，无论损失多大',
  'riskSurvey.investmentProductRatio': '5.4 您愿意投资于金融产品的资产比例为?',
  'riskSurvey.investmentProductRatio.1': '0%',
  'riskSurvey.investmentProductRatio.2': '少于20%',
  'riskSurvey.investmentProductRatio.3': '20%至40%',
  'riskSurvey.investmentProductRatio.4': '40%至60%',
  'riskSurvey.investmentProductRatio.5': '多于60%',
  'riskSurvey.expectedReturn': '5.5 您期望投资组合的年回报率为多少?',
  'riskSurvey.expectedReturn.1': '与通胀率一样',
  'riskSurvey.expectedReturn.2': '高于通胀率2%以内',
  'riskSurvey.expectedReturn.3': '高于通胀率2%至5%',
  'riskSurvey.expectedReturn.4': '高于通胀率5%至8%',
  'riskSurvey.expectedReturn.5': '高于通胀率8%',
  'riskSurvey.stockRiseResponse': '5.6 如果您持有的股票价格上涨30%，您会如何操作?',
  'riskSurvey.stockRiseResponse.1': '全部卖出',
  'riskSurvey.stockRiseResponse.2': '卖出2/3',
  'riskSurvey.stockRiseResponse.3': '卖出1/3',
  'riskSurvey.stockRiseResponse.4': '继续持有',
  'riskSurvey.fluctuation': '5.7 您可接受的投资波动范围为?',
  'riskSurvey.fluctuation.1': '±5%',
  'riskSurvey.fluctuation.2': '±10%',
  'riskSurvey.fluctuation.3': '±15%',
  'riskSurvey.fluctuation.4': '±20%',
  'riskSurvey.fluctuation.5': '大于±20%',

  // 产品管理-产品信息
  'products.list.productName': '产品名称',
  'products.list.productCode': '产品代码',
  'products.list.viewDetails': '查看详情',
  'products.list.putOn': '上架',
  'products.list.pullOff': '下架',
  'products.list.establishmentDate': '成立日期',
  'products.list.riskLevel': '风险等级',
  'products.list.shareType': '份额类型',
  'products.list.benchmarkIndex': '对标指数',
  'products.list.netValueDate': '净值日期',
  'products.list.netValue': '费后净值',
  'products.list.lockDate': '锁日期',
  'products.list.actions': '操作',
  'products.list.putOnSuccessMsg': '产品上架成功',
  'products.list.pullOffSuccessMsg': '产品已下架',

  // 产品管理-产品详情页
  'products.details.title': '产品详情',
  'products.details.basicInfo': '基本信息',
  'products.details.netValueScale': '净值规模',
  'products.details.transactionRules': '交易规则',
  'products.details.raiseAccountInfo': '募集账号信息',

  // 产品管理-产品基本信息
  'products.basicInfo.productName': '产品名称',
  'products.basicInfo.productCode': '产品代码',
  'products.basicInfo.shareType': '份额类型',
  'products.basicInfo.fundType': '基金类型',
  'products.basicInfo.riskLevel': '风险等级',
  'products.basicInfo.strategyType': '策略类型',
  'products.basicInfo.establishmentDate': '成立日期',
  'products.basicInfo.openDate': '开放期',
  'products.basicInfo.closurePeriod': '封闭期',
  'products.basicInfo.valuationMethod': '估值方法及频率',
  'products.basicInfo.performanceBenchmark': '业绩基准',
  'products.basicInfo.managementFee': '管理费用',
  'products.basicInfo.subscriptionPeriod': '申赎期限',
  'products.basicInfo.fundAdministration': '基金行政',
  'products.basicInfo.depositoryBank': '托管银行',
  'products.basicInfo.leadSeries': '领头系列',
  'products.basicInfo.productFeatures': '产品特点',
  'products.basicInfo.productIntroduction': '产品介绍',
  'products.basicInfo.fundManager': '基金经理',
  'products.basicInfo.fundManagerTitle': '基金经理头衔',
  'products.basicInfo.fundManagerPhoto': '基金经理头像',
  'products.basicInfo.fundManagerIntroduction': '基金经理简介',
  'products.basicInfo.fundManagerCompany': '基金管理人',
  'products.basicInfo.fundManagerCompanyIntroduction': '管理人简介',

  // 产品管理-交易规则
  'products.transactionRules.subscriptionStart': '认购起投',
  'products.transactionRules.subscriptionIncrement': '认购递增',
  'products.transactionRules.additionalSubscriptionStart': '追加起投',
  'products.transactionRules.additionalSubscriptionIncrement': '追加递增',
  'products.transactionRules.subscriptionFee': '认购费率',
  'products.transactionRules.minimumRedemption': '起赎份额',
  'products.transactionRules.minimumHolding': '最低持有份额',
  'products.transactionRules.redemptionFee': '赎回费率',
  'products.transactionRules.redemptionOpenPeriod': '赎回开放期',

  // 产品管理-募集账号信息
  'products.raiseAccountInfo.accountName': '账户名称',
  'products.raiseAccountInfo.bankName': '银行名称',
  'products.raiseAccountInfo.bankAccount': '银行账户',
  'products.raiseAccountInfo.bicCode': 'BIC代码',
  'products.raiseAccountInfo.remittanceNote': '汇款备注',
};

export default zh_CN;
