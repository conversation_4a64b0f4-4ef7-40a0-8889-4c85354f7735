/**
 * @author: limengyi
 * @description: 个人客户列表页
 */

import type { AuditStatus } from '@/types';
import type { CertificateType, CustomerData, RiskCategory, SearchParams } from '@/types/customer';
import type { TableColumnsType } from 'antd';
import type { FC } from 'react';

import { Button, Card, Col, Form, Input, Row, Select, Table } from 'antd';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router-dom';

import { useLocale } from '@/locales';
import { AuditStatusLanguageMap } from '@/types';
import { CertificateTypeLanguageMap, RiskResultMap } from '@/types/customer';

import { usePersonalCustomerList } from './usePersonalCustomerList';

const PersonalCustomerList: FC = () => {
  const [form] = Form.useForm();
  const { formatMessage } = useLocale();
  const navigate = useNavigate();

  // 使用自定义hook管理数据状态
  const { data, loading, total, current, pageSize, search, changePage, changePageSize } = usePersonalCustomerList();

  // 搜索表单提交
  const onSearch = (values: SearchParams) => {
    console.log('搜索参数:', values);
    search(values);
  };

  const columns: TableColumnsType<CustomerData> = [
    {
      title: formatMessage({ id: 'personalCustomer.table.customerNumber' }),
      dataIndex: 'customerNumber',
      key: 'customerNumber',
      width: 120,
    },
    {
      title: formatMessage({ id: 'personalCustomer.table.investorName' }),
      dataIndex: 'investorName',
      key: 'investorName',
      width: 120,
    },
    {
      title: formatMessage({ id: 'personalCustomer.table.certificateType' }),
      dataIndex: 'certificateType',
      key: 'certificateType',
      width: 100,
      render: (type: CertificateType) => CertificateTypeLanguageMap[type] || type,
    },
    {
      title: formatMessage({ id: 'personalCustomer.table.certificateNumber' }),
      dataIndex: 'certificateNumber',
      key: 'certificateNumber',
      width: 180,
    },
    {
      title: formatMessage({ id: 'personalCustomer.table.registrationTime' }),
      dataIndex: 'registrationTime',
      key: 'registrationTime',
      width: 120,
      render: (time: number) => dayjs(time).format('YYYY-MM-DD'),
    },
    {
      title: formatMessage({ id: 'personalCustomer.table.riskAssessment' }),
      children: [
        {
          title: formatMessage({ id: 'personalCustomer.table.riskAssessment.dueDate' }),
          dataIndex: ['riskAssessment', 'dueDate'],
          key: 'riskDueDate',
          width: 100,
          render: (time: number) => dayjs(time).format('YYYY-MM-DD'),
        },
        {
          title: formatMessage({ id: 'personalCustomer.table.riskAssessment.result' }),
          dataIndex: ['riskAssessment', 'result'],
          key: 'riskResult',
          width: 100,
          render: (result: RiskCategory) => {
            return RiskResultMap[result] || result;
          },
        },
      ],
    },
    {
      title: formatMessage({ id: 'personalCustomer.table.identityVerification' }),
      children: [
        {
          title: formatMessage({ id: 'personalCustomer.table.identityVerification.uploadDate' }),
          dataIndex: ['identityVerification', 'uploadDate'],
          key: 'identityUploadDate',
          width: 100,
          render: (time: number) => dayjs(time).format('YYYY-MM-DD'),
        },
        {
          title: formatMessage({ id: 'personalCustomer.table.identityVerification.auditStatus' }),
          dataIndex: ['identityVerification', 'auditStatus'],
          key: 'identityAuditStatus',
          width: 100,
          render: (status: AuditStatus) => AuditStatusLanguageMap[status] || '',
        },
      ],
    },
    {
      title: formatMessage({ id: 'personalCustomer.table.currentAddressVerification' }),
      children: [
        {
          title: formatMessage({ id: 'personalCustomer.table.currentAddressVerification.uploadDate' }),
          dataIndex: ['currentAddressVerification', 'uploadDate'],
          key: 'addressUploadDate',
          width: 100,
          render: (time: number) => dayjs(time).format('YYYY-MM-DD'),
        },
        {
          title: formatMessage({ id: 'personalCustomer.table.currentAddressVerification.auditStatus' }),
          dataIndex: ['currentAddressVerification', 'auditStatus'],
          key: 'addressAuditStatus',
          width: 100,
          render: (status: AuditStatus) => AuditStatusLanguageMap[status] || '',
        },
      ],
    },
    {
      title: formatMessage({ id: 'personalCustomer.table.assetVerification' }),
      children: [
        {
          title: formatMessage({ id: 'personalCustomer.table.assetVerification.uploadDate' }),
          dataIndex: ['assetVerification', 'uploadDate'],
          key: 'assetUploadDate',
          width: 100,
          render: (time: number) => dayjs(time).format('YYYY-MM-DD'),
        },
        {
          title: formatMessage({ id: 'personalCustomer.table.assetVerification.auditStatus' }),
          dataIndex: ['assetVerification', 'auditStatus'],
          key: 'assetAuditStatus',
          width: 100,
          render: (status: AuditStatus) => AuditStatusLanguageMap[status] || '',
        },
      ],
    },
    {
      title: formatMessage({ id: 'personalCustomer.table.notes' }),
      dataIndex: 'notes',
      key: 'notes',
      width: 100,
      render: (_, customer: CustomerData) => (
        <Button type="link" size="small" onClick={() => navigate(`/customer/personal/123`)}>
          {/* <Button type="link" size="small" onClick={() => navigate(`/customer/personal/${customer.id}`)}></Button> */}
          {formatMessage({ id: 'personalCustomer.table.viewDetails' })}
        </Button>
      ),
    },
  ];

  return (
    <div className="personal-customer-list">
      {/* 搜索表单 */}
      <Card style={{ marginBottom: 16 }}>
        <Form form={form} layout="inline" onFinish={onSearch} style={{ marginBottom: 16 }} className="search-form">
          <Row gutter={[16, 16]} style={{ width: '100%' }}>
            <Col span={6}>
              <Form.Item name="loginAccount" label={formatMessage({ id: 'personalCustomer.search.loginAccount' })}>
                <Input placeholder={formatMessage({ id: 'personalCustomer.search.placeholder.input' })} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="investorName" label={formatMessage({ id: 'personalCustomer.search.investorName' })}>
                <Input placeholder={formatMessage({ id: 'personalCustomer.search.placeholder.input' })} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="certificateNumber"
                label={formatMessage({ id: 'personalCustomer.search.certificateNumber' })}
              >
                <Input placeholder={formatMessage({ id: 'personalCustomer.search.placeholder.input' })} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="identityStatus" label={formatMessage({ id: 'personalCustomer.search.identityStatus' })}>
                <Select placeholder={formatMessage({ id: 'personalCustomer.search.placeholder.select' })} allowClear>
                  {Object.entries(AuditStatusLanguageMap).map(([key, value]) => (
                    <Select.Option key={key} value={key}>
                      {value}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="addressStatus" label={formatMessage({ id: 'personalCustomer.search.addressStatus' })}>
                <Select placeholder={formatMessage({ id: 'personalCustomer.search.placeholder.select' })} allowClear>
                  {Object.entries(AuditStatusLanguageMap).map(([key, value]) => (
                    <Select.Option key={key} value={key}>
                      {value}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="assetStatus" label={formatMessage({ id: 'personalCustomer.search.assetStatus' })}>
                <Select placeholder={formatMessage({ id: 'personalCustomer.search.placeholder.select' })} allowClear>
                  {Object.entries(AuditStatusLanguageMap).map(([key, value]) => (
                    <Select.Option key={key} value={key}>
                      {value}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item>
                <Button type="primary" htmlType="submit" loading={loading}>
                  {formatMessage({ id: 'personalCustomer.search.button.query' })}
                </Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={data}
          loading={loading}
          bordered
          size="middle"
          scroll={{ x: 1400 }}
          pagination={{
            current,
            pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: total => `${formatMessage({ id: 'personalCustomer.pagination.total' }, { total })}`,
            onChange: (page, size) => {
              changePage(page, size);
            },
            onShowSizeChange: (current, size) => {
              changePageSize(current, size);
            },
          }}
        />
      </Card>
    </div>
  );
};

export default PersonalCustomerList;
