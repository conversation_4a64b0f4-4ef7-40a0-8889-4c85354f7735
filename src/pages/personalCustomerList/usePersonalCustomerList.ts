import type { PaginationParams } from '@/types';
import type { CustomerData, SearchParams } from '@/types/customer';

import { message } from 'antd';
import { useCallback, useEffect, useState } from 'react';

// import { apiGetCustomerList } from '@/api/customer';
import { useLocale } from '@/locales';
import { AuditStatus } from '@/types';
import { CertificateType, RiskCategory } from '@/types/customer';

// 模拟API调用
const mockFetchCustomerList = async (
  searchParams: SearchParams,
  pagination: PaginationParams,
  formatMessage: ReturnType<typeof useLocale>['formatMessage'],
): Promise<{ data: CustomerData[]; total: number }> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1000));

  // 生成模拟数据
  const mockData: CustomerData[] = Array.from({ length: pagination.pageSize }, (_, index) => {
    const globalIndex = (pagination.current - 1) * pagination.pageSize + index + 1;

    return {
      key: `${globalIndex}`,
      customerNumber: `CU${String(globalIndex).padStart(6, '0')}`,
      investorName: searchParams.investorName ? `${searchParams.investorName}${globalIndex}` : `投资人${globalIndex}`,
      certificateType: CertificateType.ID_CARD,
      certificateNumber: `${Math.random().toString().slice(2, 8)}***********`,
      registrationTime: 1705276800000,
      riskAssessment: {
        dueDate: 1746748800000,
        result: [
          RiskCategory.CONSERVATIVE,
          RiskCategory.BALANCED,
          RiskCategory.AGGRESSIVE,
          RiskCategory.MODERATE_CONSERVATIVE,
          RiskCategory.MODERATE_AGGRESSIVE,
        ][globalIndex % 5],
      },
      identityVerification: {
        uploadDate: 1705708800000,
        auditStatus: AuditStatus.UNCHECKED,
      },
      currentAddressVerification: {
        uploadDate: 1705708800000,
        auditStatus: AuditStatus.PASSED,
      },
      assetVerification: {
        uploadDate: 1705708800000,
        auditStatus: AuditStatus.REJECTED,
      },
      notes: formatMessage({ id: 'personalCustomer.table.viewDetails' }),
    };
  });

  return {
    data: mockData,
    total: 1234, // 模拟总数据量
  };
};

export const usePersonalCustomerList = () => {
  const { formatMessage } = useLocale();

  // 状态管理
  const [data, setData] = useState<CustomerData[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchParams, setSearchParams] = useState<SearchParams>({});

  // 获取数据的核心方法
  const fetchData = useCallback(
    async (params: SearchParams = searchParams, pagination: PaginationParams = { current, pageSize }) => {
      setLoading(true);

      try {
        // TODO(LIMENGYI): 替换真实接口
        // const result = await apiGetCustomerList({
        //   ...params,
        //   current: pagination.current,
        //   pageSize: pagination.pageSize,
        // });
        const result = await mockFetchCustomerList(params, pagination, formatMessage);

        setData(result.data);
        setTotal(result.total);
      } catch (error) {
        message.error(formatMessage({ id: 'personalCustomer.error.fetchFailed' }) + error);
        setData([]);
        setTotal(0);
      } finally {
        setLoading(false);
      }
    },
    [searchParams, current, pageSize],
  );

  // 搜索方法
  const search = useCallback(
    (params: SearchParams) => {
      setSearchParams(params);
      setCurrent(1); // 搜索时重置到第一页
      fetchData(params, { current: 1, pageSize });
    },
    [pageSize, fetchData],
  );

  // 刷新当前页数据
  const refresh = useCallback(() => {
    fetchData();
  }, [fetchData]);

  // 翻页方法
  const changePage = useCallback(
    (page: number, size?: number) => {
      const newPageSize = size || pageSize;

      setCurrent(page);

      if (size && size !== pageSize) {
        setPageSize(newPageSize);
      }

      fetchData(searchParams, { current: page, pageSize: newPageSize });
    },
    [searchParams, pageSize, fetchData],
  );

  // 改变页面大小
  const changePageSize = useCallback(
    (currentPage: number, size: number) => {
      setCurrent(1); // 改变页面大小时重置到第一页
      setPageSize(size);
      fetchData(searchParams, { current: 1, pageSize: size });
    },
    [searchParams, fetchData],
  );

  // 初始化加载数据
  useEffect(() => {
    fetchData();
  }, []); // 只在组件挂载时执行一次

  return {
    // 数据状态
    data,
    loading,
    total,

    // 分页状态
    current,
    pageSize,

    // 操作方法
    search,
    refresh,
    changePage,
    changePageSize,
  };
};
