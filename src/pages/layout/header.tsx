/**
 * @author: limengyi
 * @description: 全局 Header
 */
import type { FC } from 'react';

import { LogoutOutlined, UserOutlined } from '@ant-design/icons';
import { Avatar, Dropdown, Layout, theme as antTheme, Tooltip } from 'antd';
import { createElement } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';

import { apiLogout } from '@/api/user';
// import { ReactComponent as EnUsSvg } from '@/assets/header/en_US.svg';
import { ReactComponent as LanguageSvg } from '@/assets/header/language.svg';
import { ReactComponent as MoonSvg } from '@/assets/header/moon.svg';
import { ReactComponent as SunSvg } from '@/assets/header/sun.svg';
import { ReactComponent as ZhCnSvg } from '@/assets/header/zh_CN.svg';
import { useLocale } from '@/locales';
import { setGlobalState } from '@/stores/global.store';
import { setUserItem } from '@/stores/user.store';

const { Header } = Layout;

const HeaderComponent: FC = () => {
  const { locale } = useSelector(state => state.user);
  const antThemeToken = antTheme.useToken();
  const username = localStorage.getItem('username') || 'Admin';
  const dispatch = useDispatch();
  const { formatMessage } = useLocale();
  const navigate = useNavigate();

  const selectLocale = ({ key }: { key: any }) => {
    dispatch(setUserItem({ locale: key }));
    localStorage.setItem('locale', key);
  };

  const onLogout = async () => {
    const token = localStorage.getItem('t');

    if (token) {
      const res = await apiLogout({ token });

      if (res) {
        localStorage.removeItem('t');
        localStorage.removeItem('username');

        navigate('/login');
      }
    }
  };

  return (
    <Header className="layout-page-header bg-2" style={{ backgroundColor: antThemeToken.token.colorBgContainer }}>
      <div className="title">{formatMessage({ id: 'global.title' })}</div>

      <div className="layout-page-header-main">
        <div className="actions">
          <Dropdown
            menu={{
              onClick: info => selectLocale(info),
              items: [
                {
                  key: 'zh_CN',
                  icon: <ZhCnSvg />,
                  disabled: locale === 'zh_CN',
                  label: '简体中文',
                },
                // {
                //   key: 'en_US',
                //   icon: <EnUsSvg />,
                //   disabled: locale === 'en_US',
                //   label: 'English',
                // },
              ],
            }}
          >
            <span>
              <LanguageSvg id="language-change" />
            </span>
          </Dropdown>

          <Dropdown
            menu={{
              items: [
                {
                  key: '1',
                  icon: <LogoutOutlined />,
                  label: <span onClick={onLogout}>{formatMessage({ id: 'global.logout' })}</span>,
                },
              ],
            }}
          >
            <span className="user-action">
              <Avatar size={'small'} icon={<UserOutlined />} />
              <span style={{ marginLeft: 8 }}>{username}</span>
            </span>
          </Dropdown>
        </div>
      </div>
    </Header>
  );
};

export default HeaderComponent;
