import type { MenuList } from '@/types/layout/menu';

import { Menu } from 'antd';
import { type FC } from 'react';
import { useIntl } from 'react-intl';
import { useLocation, useNavigate } from 'react-router-dom';

import { menuList } from '@/config/menu.config';

interface MenuProps {
  openKey?: string;
  onChangeOpenKey: (key?: string) => void;
  selectedKey: string;
  onChangeSelectedKey: (key: string) => void;
}

const MenuComponent: FC<MenuProps> = props => {
  const { openKey, onChangeOpenKey, selectedKey, onChangeSelectedKey } = props;
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const { formatMessage } = useIntl();

  const getTitle = (menu: MenuList[0]) => {
    return (
      <span style={{ display: 'flex', alignItems: 'center' }}>
        <span>{formatMessage({ id: menu.label })}</span>
      </span>
    );
  };

  const onMenuClick = (path: string) => {
    if (path === pathname) {
      return;
    }

    onChangeSelectedKey(path);
    navigate(path);
  };

  const onOpenChange = (keys: string[]) => {
    const key = keys.pop();

    onChangeOpenKey(key);
  };

  return (
    <Menu
      mode="inline"
      selectedKeys={[selectedKey]}
      openKeys={openKey ? [openKey] : []}
      onOpenChange={onOpenChange}
      onClick={e => onMenuClick(e.key)}
      className="layout-page-sider-menu text-2"
      items={menuList.map(menu => {
        return menu.children
          ? {
              key: menu.key,
              label: getTitle(menu),
              children: menu.children.map(child => ({
                key: child.key,
                label: formatMessage({ id: child.label }),
              })),
            }
          : {
              key: menu.path,
              label: getTitle(menu),
            };
      })}
    ></Menu>
  );
};

export default MenuComponent;
