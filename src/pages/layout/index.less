.layout-page {
  height: 100%;
  &-header {
    padding: 0 !important;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 9;
    box-shadow: 0 4px 10px #dddddd;

    .title {
      font-size: 20px;
      font-weight: bold;
      padding: 0 20px;
    }

    &-main {
      padding: 0 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  &-sider {
    box-sizing: border-box;
    margin-bottom: 10px;
  }
  &-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    padding: 10px;
    > :nth-child(1) .ant-tabs-bar {
      padding: 6px 0 0;
    }

    > :nth-child(2) {
      flex: auto;
      overflow: auto;
      padding: 6px;
      box-sizing: border-box;
      .innerText {
        padding: 24px;
        border-radius: 2px;
        display: block;
        line-height: 32px;
        font-size: 16px;
      }
    }
  }
  &-footer {
    text-align: center;
    padding: 14px 20px;
    font-size: 12px;
  }
  .actions {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    > * {
      margin-left: 30px;
      height: 100%;
      display: flex;
      align-items: center;
    }
  }
  .user-action {
    cursor: pointer;
  }
}

.layout-page-sider-menu {
  border-right: none !important;
}
.ant-menu-inline-collapsed {
  width: 79px !important;
}

.notice-description {
  font-size: 12px;
  &-datetime {
    margin-top: 4px;
    line-height: 1.5;
  }
}

.notice-title {
  display: flex;
  justify-content: space-between;
}

.tagsView-extra {
  height: 100%;
  width: 50px;
  cursor: pointer;
  display: block;
  line-height: 40px;
  text-align: center;
}