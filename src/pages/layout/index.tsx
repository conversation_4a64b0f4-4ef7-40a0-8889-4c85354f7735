import type { FC } from 'react';

import './index.less';

import { Layout, theme as antTheme } from 'antd';
import { Suspense, useEffect, useState } from 'react';
import { Outlet, useLocation } from 'react-router';

import { findRouteByPath, getFirstPathCode } from '@/utils/findPath';

import HeaderComponent from './header';
import MenuComponent from './menu';

const { Sider, Content } = Layout;
const CollapsedWidth = 80;

const LayoutPage: FC = () => {
  const location = useLocation();
  const [openKey, setOpenKey] = useState<string>();
  const [selectedKey, setSelectedKey] = useState<string>(location.pathname);
  const token = antTheme.useToken();

  useEffect(() => {
    const code = getFirstPathCode(location.pathname);
    const route = findRouteByPath(location.pathname);

    setOpenKey(code);
    setSelectedKey(route?.parentPath || location.pathname);
  }, [location.pathname]);

  return (
    <Layout className="layout-page">
      <HeaderComponent />
      <Layout>
        <Sider
          className="layout-page-sider"
          trigger={null}
          collapsible
          style={{ backgroundColor: token.token.colorBgContainer }}
          collapsedWidth={CollapsedWidth}
          breakpoint="md"
        >
          <MenuComponent
            openKey={openKey}
            onChangeOpenKey={k => setOpenKey(k)}
            selectedKey={selectedKey}
            onChangeSelectedKey={k => setSelectedKey(k)}
          />
        </Sider>

        <Content className="layout-page-content">
          <Suspense fallback={null}>
            <Outlet />
          </Suspense>
        </Content>
      </Layout>
    </Layout>
  );
};

export default LayoutPage;
