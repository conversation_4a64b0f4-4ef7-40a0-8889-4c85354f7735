/**
 * <AUTHOR>
 * @Title 登录页
 */
import type { LoginParams } from '@/types/user/login';
import type { FC } from 'react';

import './index.less';

import { Button, Checkbox, Form, Input, message } from 'antd';
import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import { apiLogin } from '@/api/user';
import LoginImg from '@/assets/images/login-img.png';
import { LocaleFormatter, useLocale } from '@/locales';
import { formatSearch } from '@/utils/formatSearch';

const LoginPage: FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { formatMessage } = useLocale();

  const [form] = Form.useForm<LoginParams>();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const loginName = localStorage.getItem('loginName');

    if (loginName) {
      form.setFieldsValue({
        username: loginName,
        rememberUser: true,
      });
    } else {
      form.setFieldsValue({
        username: '',
        rememberUser: false,
      });
    }
  }, []);

  const onFinished = async (form: LoginParams) => {
    setLoading(true);

    try {
      // TODO(LIMENGYI): 确认返回格式
      const { result, status } = await apiLogin({
        username: form.username,
        password: form.password,
      });

      if (status) {
        localStorage.setItem('t', result.token);
        localStorage.setItem('username', result.username);

        if (form.rememberUser) {
          localStorage.setItem('loginName', result.username);
        } else {
          localStorage.removeItem('loginName');
        }
      }

      if (!!result) {
        const search = formatSearch(location.search);
        const from = search.from || { pathname: '/' };

        navigate(from);
      }
    } catch (error) {
      message.error(formatMessage({ id: 'login.error' }));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-page">
      <img src={LoginImg} alt="Login" className="login-page-img" />
      <div className="login-page-form-wrapper">
        <Form form={form} onFinish={onFinished} className="login-page-form">
          <h2>{formatMessage({ id: 'login.title' }, { name: 'POLARIS CAPITAL' })}</h2>
          <Form.Item
            name="username"
            rules={[
              {
                required: true,
                message: formatMessage({
                  id: 'login.enterUsernameMessage',
                }),
              },
            ]}
          >
            <Input
              placeholder={formatMessage({
                id: 'login.username',
              })}
            />
          </Form.Item>
          <Form.Item
            name="password"
            rules={[
              {
                required: true,
                message: formatMessage({
                  id: 'login.enterPasswordMessage',
                }),
              },
            ]}
          >
            <Input
              type="password"
              placeholder={formatMessage({
                id: 'login.password',
              })}
            />
          </Form.Item>
          <Form.Item name="rememberUser" valuePropName="checked">
            <Checkbox>
              <LocaleFormatter id="login.rememberUser" />
            </Checkbox>
          </Form.Item>
          <Button loading={loading} htmlType="submit" type="primary" size="large" className="login-page-form_button">
            <LocaleFormatter id="login.button" />
          </Button>
        </Form>
      </div>
    </div>
  );
};

export default LoginPage;
