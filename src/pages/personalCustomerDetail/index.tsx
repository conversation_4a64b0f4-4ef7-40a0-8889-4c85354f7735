/**
 * @author: lime<PERSON><PERSON>
 * @description: 个人客户详情页
 */
import type { TabsProps } from 'antd';
import type { FC } from 'react';

import './index.less';

import { Card, Tabs } from 'antd';
import { useState } from 'react';

import { useLocale } from '@/locales';

import DetailHeader from '../../components/DetailHeader';
import Loading from '../../components/Loading';
import BaseInfo from './components/BaseInfo';
import RiskInvestment from './components/RiskInvestment';
import { usePersonalCustomerDetail } from './usePersonalCustomerDetail';

const PersonalCustomerDetail: FC = () => {
  const { formatMessage } = useLocale();
  const [activeTab, setActiveTab] = useState<string>('basicInfo');

  const { data, loading } = usePersonalCustomerDetail();

  // Tab配置
  const tabItems: TabsProps['items'] = [
    {
      key: 'basicInfo',
      label: formatMessage({ id: 'personalCustomerDetail.tabs.basicInfo' }),
      children: <BaseInfo data={data} />,
    },
    {
      key: 'riskAssessment',
      label: formatMessage({ id: 'personalCustomerDetail.tabs.riskAssessment' }),
      children: <RiskInvestment data={data} />,
    },
    {
      key: 'identityVerification',
      label: formatMessage({ id: 'personalCustomerDetail.tabs.identityVerification' }),
      children: (
        <Card className="info-card">{formatMessage({ id: 'personalCustomerDetail.tabs.identityVerification' })}</Card>
      ),
    },
    {
      key: 'residenceVerification',
      label: formatMessage({ id: 'personalCustomerDetail.tabs.residenceVerification' }),
      children: (
        <Card className="info-card">
          <div className="info-row">
            <div className="info-item">
              <div className="label">
                {formatMessage({ id: 'personalCustomerDetail.residenceInfo.verificationDate' })}
              </div>
              <div className="value">{data?.residenceInfo.verificationDate}</div>
            </div>
            <div className="info-item">
              <div className="label">
                {formatMessage({ id: 'personalCustomerDetail.residenceInfo.residenceAddress' })}
              </div>
              <div className="value">{data?.residenceInfo.residenceAddress}</div>
            </div>
          </div>
        </Card>
      ),
    },
    {
      key: 'assetVerification',
      label: formatMessage({ id: 'personalCustomerDetail.tabs.assetVerification' }),
      children: (
        <Card className="info-card">{formatMessage({ id: 'personalCustomerDetail.tabs.assetVerification' })}</Card>
      ),
    },
  ];

  return (
    <div className="personal-customer-detail">
      <DetailHeader style={{ marginBottom: 16 }} title={formatMessage({ id: 'personalCustomerDetail.title' })} />

      <Card className="content-wrapper">
        <Tabs
          className="tabs-container"
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="large"
          tabPosition="top"
        />
        {loading && <Loading center />}
      </Card>
    </div>
  );
};

export default PersonalCustomerDetail;
