.personal-customer-risk-investment {
  .info-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 16px;
    
    .info-item {
      width: 50%;
      padding-right: 32px;
      margin-bottom: 16px;
      
      @media (max-width: 1200px) {
        width: 50%;
      }
      
      @media (max-width: 768px) {
        width: 100%;
      }
      
      .label {
        color: rgba(0, 0, 0, 1);
        margin-bottom: 4px;
        font-weight: 500;
      }
      
      .value {
        color: rgba(0, 0, 0, 0.5);
        font-weight: 500;
        line-height: 1.5;
        word-break: break-all;
      }
    }
  }

  .risk-survey-form {
    margin-top: 24px;
    
    .survey-section {
      margin-bottom: 24px;
      padding: 16px;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      
      .section-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 16px;
        color: #262626;
        border-bottom: 1px solid #f0f0f0;
        padding-bottom: 8px;
      }
      
      .question-item {
        margin-bottom: 20px;
        
        .question-title {
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 12px;
          color: #262626;
        }
        
        .option-row {
          margin-bottom: 8px;
          display: flex;
          align-items: center;
          
          .ant-radio,
          .ant-checkbox {
            margin-right: 8px;
          }
        }
      }

      .asset-type-section {
        margin-top: 12px;
        padding-top: 12px;
        border-top: 1px dashed #d9d9d9;
        
        .asset-type-label {
          font-weight: 500;
          margin-right: 12px;
          color: #595959;
        }

        .ant-checkbox-group {
          .ant-checkbox-wrapper {
            margin-right: 16px;
            margin-bottom: 8px;
          }
        }
      }

      .additional-info {
        margin: 0;
        color: rgba(0, 0, 0, 0.5);
      }
      
      .ant-checkbox-group,
      .ant-radio-group {
        width: 100%;
      }
    }
  }

  .flex-column {
    display: flex;
    flex-direction: column;
  }
}