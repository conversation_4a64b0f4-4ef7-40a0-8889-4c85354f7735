/**
 * @author: limengyi
 * @description: 个人客户详情-风险问卷
 */

import type { CustomerDetailData } from '@/types/customer';
import type { FC } from 'react';

import './index.less';

import { Card, Checkbox, Input, Radio } from 'antd';
import dayjs from 'dayjs';
import { useState } from 'react';

import { useLocale } from '@/locales';
import { RiskResultMap } from '@/types/customer';

const BaseInfo: FC<{ data: CustomerDetailData | null }> = ({ data }) => {
  const { formatMessage } = useLocale();
  // TODO(LIMENGYI): 替换真实数据
  const [formData] = useState<Record<string, any>>({});

  return (
    <Card className="personal-customer-risk-investment">
      {/* 基本风险评估信息 */}
      <div className="info-row">
        <div className="info-item">
          <div className="label">{formatMessage({ id: 'personalCustomerDetail.riskAssessment.score' })}</div>
          <div className="value">{data?.riskAssessment.score || '-'}</div>
        </div>
        <div className="info-item">
          <div className="label">{formatMessage({ id: 'personalCustomerDetail.riskAssessment.date' })}</div>
          <div className="value">{data ? dayjs(data.riskAssessment.assessmentDate).format('YYYY-MM-DD') : '-'}</div>
        </div>
        <div className="info-item">
          <div className="label">{formatMessage({ id: 'personalCustomerDetail.riskAssessment.assessmentResult' })}</div>
          <div className="value">{data ? RiskResultMap[data.riskAssessment.assessmentResult] : '-'}</div>
        </div>
        <div className="info-item">
          <div className="label">{formatMessage({ id: 'personalCustomerDetail.riskAssessment.assessmentExpiry' })}</div>
          <div className="value">{data ? dayjs(data.riskAssessment.assessmentExpiry).format('YYYY-MM-DD') : '-'}</div>
        </div>
      </div>

      {/* 风险问卷详情 */}
      <div className="risk-survey-form">
        {/* (1) 个人及财务资料 */}
        <div className="survey-section">
          <h3 className="section-title">{formatMessage({ id: 'riskSurvey.section1' })}</h3>

          {/* 1.1 教育程度 */}
          <div className="question-item">
            <div className="question-title">{formatMessage({ id: 'riskSurvey.educationLevel' })}</div>
            <Radio.Group value={formData.educationLevel}>
              <div className="option-row">
                <Radio value="primary">(A) {formatMessage({ id: 'riskSurvey.educationLevel.primary' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="secondary">(B) {formatMessage({ id: 'riskSurvey.educationLevel.secondary' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="postSecondary">
                  (C) {formatMessage({ id: 'riskSurvey.educationLevel.postSecondary' })}
                </Radio>
              </div>
              <div className="option-row">
                <Radio value="university">(D) {formatMessage({ id: 'riskSurvey.educationLevel.university' })}</Radio>
              </div>
            </Radio.Group>
            <p className="additional-info">{formatMessage({ id: 'riskSurvey.educationLevel.additional' })}</p>
          </div>

          {/* 1.2 年收入 */}
          <div className="question-item">
            <div className="question-title">{formatMessage({ id: 'riskSurvey.annualIncome' })}</div>
            <Radio.Group value={formData.annualIncome}>
              <div className="option-row">
                <Radio value="income1">{formatMessage({ id: 'riskSurvey.annualIncome.1' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="income2">{formatMessage({ id: 'riskSurvey.annualIncome.2' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="income3">{formatMessage({ id: 'riskSurvey.annualIncome.3' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="income4">
                  {formatMessage({ id: 'riskSurvey.annualIncome.4' })}
                  <Input
                    style={{ marginLeft: 8, width: 200 }}
                    placeholder={formatMessage({ id: 'riskSurvey.annualIncome.placeholder' })}
                    value={formData.incomeSpecify}
                  />
                </Radio>
              </div>
              <div className="option-row">
                <Radio value="income5">
                  {formatMessage({ id: 'riskSurvey.annualIncome.5' })}
                  <Input
                    style={{ marginLeft: 8, width: 200 }}
                    placeholder={formatMessage({ id: 'riskSurvey.annualIncome.reason' })}
                    value={formData.incomeSpecify}
                  />
                </Radio>
              </div>
            </Radio.Group>
          </div>

          {/* 1.3 资产净值 */}
          <div className="question-item">
            <div className="question-title">{formatMessage({ id: 'riskSurvey.netWorth' })}</div>
            <Radio.Group value={formData.netWorth}>
              <div className="option-row">
                <Radio value="netWorth1">{formatMessage({ id: 'riskSurvey.netWorth.1' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="netWorth2">{formatMessage({ id: 'riskSurvey.netWorth.2' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="netWorth3">{formatMessage({ id: 'riskSurvey.netWorth.3' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="netWorth4">
                  {formatMessage({ id: 'riskSurvey.netWorth.4' })}
                  <Input
                    style={{ marginLeft: 8, width: 200 }}
                    placeholder={formatMessage({ id: 'riskSurvey.annualIncome.placeholder' })}
                    value={formData.netWorthSpecify}
                  />
                </Radio>
              </div>
            </Radio.Group>

            {/* 资产类型 */}
            <div className="asset-type-section">
              <span className="asset-type-label">{formatMessage({ id: 'riskSurvey.assetType' })}:</span>
              <Checkbox.Group value={formData.assetType}>
                <Checkbox value="property">
                  {formatMessage({ id: 'riskSurvey.assetType.property' })}
                  <Input style={{ width: 60, margin: '0 4px' }} />
                  (HK$)
                </Checkbox>
                <Checkbox value="cash">
                  {formatMessage({ id: 'riskSurvey.assetType.cash' })}
                  <Input style={{ width: 60, margin: '0 4px' }} />
                  (HK$)
                </Checkbox>
                <Checkbox value="stock">
                  {formatMessage({ id: 'riskSurvey.assetType.stock' })}
                  <Input style={{ width: 60, margin: '0 4px' }} />
                  (HK$)
                </Checkbox>
                <Checkbox value="others">
                  {formatMessage({ id: 'riskSurvey.assetType.others' })}
                  <Input style={{ width: 60, margin: '0 4px' }} />
                  (HK$)
                </Checkbox>
              </Checkbox.Group>
            </div>
          </div>
        </div>
        {/* (2) 资金来源 */}
        <div className="survey-section">
          <h3 className="section-title">{formatMessage({ id: 'riskSurvey.sourceOfFunds' })}</h3>
          <Checkbox.Group value={formData.sourceOfFunds} className="flex-column">
            <div className="option-row">
              <Checkbox value="salary">{formatMessage({ id: 'riskSurvey.sourceOfFunds.salary' })}</Checkbox>
            </div>
            <div className="option-row">
              <Checkbox value="business">{formatMessage({ id: 'riskSurvey.sourceOfFunds.business' })}</Checkbox>
            </div>
            <div className="option-row">
              <Checkbox value="realEstate">
                {formatMessage({ id: 'riskSurvey.sourceOfFunds.saleOfRealEstate' })}
              </Checkbox>
            </div>
            <div className="option-row">
              <Checkbox value="savings">{formatMessage({ id: 'riskSurvey.sourceOfFunds.savings' })}</Checkbox>
            </div>
            <div className="option-row">
              <Checkbox value="others">{formatMessage({ id: 'riskSurvey.sourceOfFunds.others' })}</Checkbox>
            </div>
            <div className="option-row">
              <Checkbox value="investment">
                {formatMessage({ id: 'riskSurvey.sourceOfFunds.investmentIncome' })}
              </Checkbox>
            </div>
          </Checkbox.Group>

          {/* 投资获利（股票/债券/其他） */}
          <div className="asset-type-section">
            <span className="asset-type-label">
              {formatMessage({ id: 'riskSurvey.sourceOfFunds.investmentSource' })}:
            </span>
            <Checkbox.Group value={formData.assetType}>
              <Checkbox value="stock">
                {formatMessage({ id: 'riskSurvey.sourceOfFunds.stock' })}
                <Input style={{ width: 60, margin: '0 4px' }} />
                {formatMessage({ id: 'global.year' })}
              </Checkbox>
              <Checkbox value="bonds">
                {formatMessage({ id: 'riskSurvey.sourceOfFunds.bonds' })}
                <Input style={{ width: 60, margin: '0 4px' }} />
                {formatMessage({ id: 'global.year' })}
              </Checkbox>
              <Checkbox value="others">
                {formatMessage({ id: 'riskSurvey.assetType.others' })}：
                <Input
                  style={{ width: 200, margin: '0 4px' }}
                  placeholder={formatMessage({ id: 'riskSurvey.annualIncome.placeholder' })}
                />
                <Input style={{ margin: '0 4px', width: 60 }} />
                {formatMessage({ id: 'global.year' })}
              </Checkbox>
            </Checkbox.Group>
          </div>
        </div>
        {/* (3) 财富来源 */}
        <div className="survey-section">
          <h3 className="section-title">{formatMessage({ id: 'riskSurvey.sourceOfWealth' })}</h3>
          <Checkbox.Group value={formData.sourceOfWealth} className="flex-column">
            <div className="option-row">
              <Checkbox value="salary">{formatMessage({ id: 'riskSurvey.sourceOfWealth.salary' })}</Checkbox>
            </div>
            <div className="option-row">
              <Checkbox value="business">{formatMessage({ id: 'riskSurvey.sourceOfWealth.business' })}</Checkbox>
            </div>
            <div className="option-row">
              <Checkbox value="inheritance">{formatMessage({ id: 'riskSurvey.sourceOfWealth.inheritance' })}</Checkbox>
            </div>
            <div className="option-row">
              <Checkbox value="others">{formatMessage({ id: 'riskSurvey.sourceOfWealth.others' })}</Checkbox>
            </div>
            <div className="option-row">
              <Checkbox value="investment">{formatMessage({ id: 'riskSurvey.sourceOfWealth.investment' })}</Checkbox>
            </div>
          </Checkbox.Group>

          {/* 投资获利（股票/债券/其他） */}
          <div className="asset-type-section">
            <span className="asset-type-label">
              {formatMessage({ id: 'riskSurvey.sourceOfFunds.investmentSource' })}:
            </span>
            <Checkbox.Group value={formData.assetType}>
              <Checkbox value="stock">
                {formatMessage({ id: 'riskSurvey.sourceOfFunds.stock' })}
                <Input style={{ width: 60, margin: '0 4px' }} />
                {formatMessage({ id: 'global.year' })}
              </Checkbox>
              <Checkbox value="bonds">
                {formatMessage({ id: 'riskSurvey.sourceOfFunds.bonds' })}
                <Input style={{ width: 60, margin: '0 4px' }} />
                {formatMessage({ id: 'global.year' })}
              </Checkbox>
              <Checkbox value="others">
                {formatMessage({ id: 'riskSurvey.assetType.others' })}：
                <Input
                  style={{ width: 200, margin: '0 4px' }}
                  placeholder={formatMessage({ id: 'riskSurvey.annualIncome.placeholder' })}
                />
                <Input style={{ margin: '0 4px', width: 60 }} />
                {formatMessage({ id: 'global.year' })}
              </Checkbox>
            </Checkbox.Group>
          </div>
        </div>
        {/* (4) 预期交易活动 */}
        <div className="survey-section">
          <h3 className="section-title">{formatMessage({ id: 'riskSurvey.anticipatedLevelOfActivity' })}</h3>
          <Radio.Group value={formData.anticipatedActivity}>
            <div className="option-row">
              <Radio value="activity1">{formatMessage({ id: 'riskSurvey.anticipatedLevelOfActivity.1' })}</Radio>
            </div>
            <div className="option-row">
              <Radio value="activity2">{formatMessage({ id: 'riskSurvey.anticipatedLevelOfActivity.2' })}</Radio>
            </div>
            <div className="option-row">
              <Radio value="activity3">{formatMessage({ id: 'riskSurvey.anticipatedLevelOfActivity.3' })}</Radio>
            </div>
            <div className="option-row">
              <Radio value="activity4">
                {formatMessage({ id: 'riskSurvey.anticipatedLevelOfActivity.4' })}
                <Input
                  style={{ marginLeft: 8, width: 200 }}
                  placeholder={formatMessage({ id: 'riskSurvey.annualIncome.placeholder' })}
                  value={formData.activitySpecify}
                />
              </Radio>
            </div>
          </Radio.Group>
        </div>
        {/* (5) 风险评估问卷 */}
        <div className="survey-section">
          <h3 className="section-title">{formatMessage({ id: 'riskSurvey.riskProfiling' })}</h3>

          {/* 5.1 最长投资年期 */}
          <div className="question-item">
            <div className="question-title">{formatMessage({ id: 'riskSurvey.longestInvestmentPeriod' })}</div>
            <Radio.Group value={formData.longestInvestmentPeriod}>
              <div className="option-row">
                <Radio value="period1">(A) {formatMessage({ id: 'riskSurvey.longestInvestmentPeriod.1' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="period2">(B) {formatMessage({ id: 'riskSurvey.longestInvestmentPeriod.2' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="period3">(C) {formatMessage({ id: 'riskSurvey.longestInvestmentPeriod.3' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="period4">(D) {formatMessage({ id: 'riskSurvey.longestInvestmentPeriod.4' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="period5">(E) {formatMessage({ id: 'riskSurvey.longestInvestmentPeriod.5' })}</Radio>
              </div>
            </Radio.Group>
          </div>

          {/* 5.2 投资目标 */}
          <div className="question-item">
            <div className="question-title">{formatMessage({ id: 'riskSurvey.investmentGoal' })}</div>
            <Radio.Group value={formData.investmentGoal}>
              <div className="option-row">
                <Radio value="goal1">(A) {formatMessage({ id: 'riskSurvey.investmentGoal.1' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="goal2">(B) {formatMessage({ id: 'riskSurvey.investmentGoal.2' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="goal3">(C) {formatMessage({ id: 'riskSurvey.investmentGoal.3' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="goal4">(D) {formatMessage({ id: 'riskSurvey.investmentGoal.4' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="goal5">(E) {formatMessage({ id: 'riskSurvey.investmentGoal.5' })}</Radio>
              </div>
            </Radio.Group>
          </div>

          {/* 5.3 投资下跌反应 */}
          <div className="question-item">
            <div className="question-title">{formatMessage({ id: 'riskSurvey.dropResponse' })}</div>
            <Radio.Group value={formData.dropResponse}>
              <div className="option-row">
                <Radio value="drop1">(A) {formatMessage({ id: 'riskSurvey.dropResponse.1' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="drop2">(B) {formatMessage({ id: 'riskSurvey.dropResponse.2' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="drop3">(C) {formatMessage({ id: 'riskSurvey.dropResponse.3' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="drop4">(D) {formatMessage({ id: 'riskSurvey.dropResponse.4' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="drop5">(E) {formatMessage({ id: 'riskSurvey.dropResponse.5' })}</Radio>
              </div>
            </Radio.Group>
          </div>

          {/* 5.4 投资金融产品资产比例 */}
          <div className="question-item">
            <div className="question-title">{formatMessage({ id: 'riskSurvey.investmentProductRatio' })}</div>
            <Radio.Group value={formData.investmentProductRatio}>
              <div className="option-row">
                <Radio value="ratio1">(A) {formatMessage({ id: 'riskSurvey.investmentProductRatio.1' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="ratio2">(B) {formatMessage({ id: 'riskSurvey.investmentProductRatio.2' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="ratio3">(C) {formatMessage({ id: 'riskSurvey.investmentProductRatio.3' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="ratio4">(D) {formatMessage({ id: 'riskSurvey.investmentProductRatio.4' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="ratio5">(E) {formatMessage({ id: 'riskSurvey.investmentProductRatio.5' })}</Radio>
              </div>
            </Radio.Group>
          </div>

          {/* 5.5 期望年回报率 */}
          <div className="question-item">
            <div className="question-title">{formatMessage({ id: 'riskSurvey.expectedReturn' })}</div>
            <Radio.Group value={formData.expectedReturn}>
              <div className="option-row">
                <Radio value="return1">(A) {formatMessage({ id: 'riskSurvey.expectedReturn.1' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="return2">(B) {formatMessage({ id: 'riskSurvey.expectedReturn.2' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="return3">(C) {formatMessage({ id: 'riskSurvey.expectedReturn.3' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="return4">(D) {formatMessage({ id: 'riskSurvey.expectedReturn.4' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="return5">(E) {formatMessage({ id: 'riskSurvey.expectedReturn.5' })}</Radio>
              </div>
            </Radio.Group>
          </div>

          {/* 5.6 股票上涨反应 */}
          <div className="question-item">
            <div className="question-title">{formatMessage({ id: 'riskSurvey.stockRiseResponse' })}</div>
            <Radio.Group value={formData.stockRiseResponse}>
              <div className="option-row">
                <Radio value="rise1">(A) {formatMessage({ id: 'riskSurvey.stockRiseResponse.1' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="rise2">(B) {formatMessage({ id: 'riskSurvey.stockRiseResponse.2' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="rise3">(C) {formatMessage({ id: 'riskSurvey.stockRiseResponse.3' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="rise4">(D) {formatMessage({ id: 'riskSurvey.stockRiseResponse.4' })}</Radio>
              </div>
            </Radio.Group>
          </div>

          {/* 5.7 投资波动范围 */}
          <div className="question-item">
            <div className="question-title">{formatMessage({ id: 'riskSurvey.fluctuation' })}</div>
            <Radio.Group value={formData.fluctuation}>
              <div className="option-row">
                <Radio value="fluctuation1">(A) {formatMessage({ id: 'riskSurvey.fluctuation.1' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="fluctuation2">(B) {formatMessage({ id: 'riskSurvey.fluctuation.2' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="fluctuation3">(C) {formatMessage({ id: 'riskSurvey.fluctuation.3' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="fluctuation4">(D) {formatMessage({ id: 'riskSurvey.fluctuation.4' })}</Radio>
              </div>
              <div className="option-row">
                <Radio value="fluctuation5">(E) {formatMessage({ id: 'riskSurvey.fluctuation.5' })}</Radio>
              </div>
            </Radio.Group>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default BaseInfo;
