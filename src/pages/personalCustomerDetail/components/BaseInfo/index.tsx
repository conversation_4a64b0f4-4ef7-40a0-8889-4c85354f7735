/**
 * @author: limengyi
 * @description: 个人客户详情-基本信息
 */

import type { CustomerDetailData } from '@/types/customer';
import type { FC } from 'react';

import './index.less';

import { Card } from 'antd';
import dayjs from 'dayjs';

import { useLocale } from '@/locales';
import { RiskResultMap } from '@/types/customer';

const BaseInfo: FC<{ data: CustomerDetailData | null }> = ({ data }) => {
  const { formatMessage } = useLocale();

  return (
    <Card className="personal-customer-base-info">
      <div className="info-row">
        <div className="info-item">
          <div className="label">{formatMessage({ id: 'personalCustomer.search.investorName' })}</div>
          <div className="value">{data?.basicInfo.investorName || '-'}</div>
        </div>
        <div className="info-item">
          <div className="label">{formatMessage({ id: 'personalCustomerDetail.basicInfo.loginAccount' })}</div>
          <div className="value">{data?.basicInfo.loginAccount || '-'}</div>
        </div>
        <div className="info-item">
          <div className="label">{formatMessage({ id: 'personalCustomerDetail.basicInfo.registrationTime' })}</div>
          <div className="value">
            {data ? dayjs(data.basicInfo.registrationTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
          </div>
        </div>
        <div className="info-item">
          <div className="label">{formatMessage({ id: 'personalCustomerDetail.basicInfo.certificateType' })}</div>
          <div className="value">{data?.basicInfo.certificateType || '-'}</div>
        </div>
        <div className="info-item">
          <div className="label">{formatMessage({ id: 'personalCustomerDetail.basicInfo.certificateNumber' })}</div>
          <div className="value">{data?.basicInfo.certificateNumber || '-'}</div>
        </div>
        <div className="info-item">
          <div className="label">{formatMessage({ id: 'personalCustomerDetail.basicInfo.certificateExpiry' })}</div>
          <div className="value">{data ? dayjs(data.basicInfo.certificateExpiry).format('YYYY-MM-DD') : '-'}</div>
        </div>
        <div className="info-item">
          <div className="label">{formatMessage({ id: 'personalCustomerDetail.basicInfo.gender' })}</div>
          <div className="value">{data?.basicInfo.gender || '-'}</div>
        </div>
        <div className="info-item">
          <div className="label">{formatMessage({ id: 'personalCustomerDetail.basicInfo.birthDate' })}</div>
          <div className="value">{data ? dayjs(data.basicInfo.birthDate).format('YYYY-MM-DD') : '-'}</div>
        </div>
        <div className="info-item">
          <div className="label">{formatMessage({ id: 'personalCustomerDetail.basicInfo.country' })}</div>
          <div className="value">{data?.basicInfo.country || '-'}</div>
        </div>
        <div className="info-item">
          <div className="label">{formatMessage({ id: 'personalCustomerDetail.riskAssessment.assessmentDate' })}</div>
          <div className="value">{data ? dayjs(data.riskAssessment.assessmentDate).format('YYYY-MM-DD') : '-'}</div>
        </div>
        <div className="info-item">
          <div className="label">{formatMessage({ id: 'personalCustomerDetail.riskAssessment.assessmentResult' })}</div>
          <div className="value">{data ? RiskResultMap[data.riskAssessment.assessmentResult] : '-'}</div>
        </div>
        <div className="info-item">
          <div className="label">{formatMessage({ id: 'personalCustomerDetail.riskAssessment.assessmentExpiry' })}</div>
          <div className="value">{data ? dayjs(data.riskAssessment.assessmentExpiry).format('YYYY-MM-DD') : '-'}</div>
        </div>
        <div className="info-item">
          <div className="label">{formatMessage({ id: 'personalCustomerDetail.residenceInfo.verificationDate' })}</div>
          <div className="value">{data ? dayjs(data.residenceInfo.verificationDate).format('YYYY-MM-DD') : '-'}</div>
        </div>
        <div className="info-item">
          <div className="label">{formatMessage({ id: 'personalCustomerDetail.residenceInfo.residenceAddress' })}</div>
          <div className="value">{data?.residenceInfo.residenceAddress || '-'}</div>
        </div>
      </div>
    </Card>
  );
};

export default BaseInfo;
