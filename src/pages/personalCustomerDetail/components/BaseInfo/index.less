.personal-customer-base-info {
  .info-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 16px;
    
    .info-item {
      width: 33.33%;
      padding-right: 32px;
      margin-bottom: 16px;
      
      @media (max-width: 1200px) {
        width: 50%;
      }
      
      @media (max-width: 768px) {
        width: 100%;
      }
      
      .label {
        color: rgba(0, 0, 0, 1);
        margin-bottom: 4px;
        font-weight: 500;
      }
      
      .value {
        color: rgba(0, 0, 0, 0.5);
        font-weight: 500;
        line-height: 1.5;
        word-break: break-all;
      }
    }
  }
}