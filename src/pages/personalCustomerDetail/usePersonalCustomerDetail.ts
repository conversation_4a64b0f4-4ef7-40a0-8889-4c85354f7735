import { message } from 'antd';
import { useCallback, useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

import { apiGetCustomerDetail } from '@/api/customer';
import { useLocale } from '@/locales';
import { type CustomerDetailData, RiskCategory } from '@/types/customer';

// 模拟API调用
const mockFetchCustomerDetail = async (customerId: string): Promise<{ data: CustomerDetailData }> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1000));

  // 生成模拟数据
  const mockData: CustomerDetailData = {
    basicInfo: {
      investorName: '张三',
      loginAccount: '***********',
      registrationTime: *************,
      certificateType: '大陆身份证',
      certificateNumber: '432321197809091234',
      certificateExpiry: *************,
      gender: '男',
      birthDate: ************,
      country: '中国',
    },
    riskAssessment: {
      assessmentDate: *************,
      assessmentResult: RiskCategory.AGGRESSIVE,
      assessmentExpiry: *************,
      score: 39,
    },
    residenceInfo: {
      verificationDate: *************,
      residenceAddress: '广东省广州市海珠区双龙大厦',
    },
  };

  return { data: mockData };
};

export const usePersonalCustomerDetail = () => {
  const { customerId } = useParams<{ customerId: string }>();
  const [loading, setLoading] = useState<boolean>(false);
  const [data, setData] = useState<CustomerDetailData | null>(null);
  const { formatMessage } = useLocale();

  // 获取数据的核心方法
  const fetchData = useCallback(async () => {
    if (!customerId) {
      console.error('Customer ID is required');

      return;
    }

    setLoading(true);

    try {
      // TODO(LIMENGYI): 替换真实接口
      // const result = await apiGetCustomerDetail(customerId);
      const result = await mockFetchCustomerDetail(customerId);

      setData(result.data);
    } catch (error) {
      message.error(formatMessage({ id: 'personalCustomer.error.fetchFailed' }) + error);
      setData(null);
    } finally {
      setLoading(false);
    }
  }, [customerId]);

  // 初始化加载数据
  useEffect(() => {
    fetchData();
  }, []);

  return {
    data,
    loading,
  };
};
