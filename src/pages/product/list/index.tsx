/**
 * @Owners ljh
 * @Title 产品列表
 */
import './index.less';

import { Button, Flex, message, Select, Space, Spin, Table } from 'antd';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

import { SearchQueryFilter, type SearchQueryFilterProps } from '@/components';
import { useSearchTable } from '@/hooks/useSearchTable';
import { useLocale } from '@/locales';

import { useProductCodeOptions } from './hooks/useProductCodeOptions';
import { useProductNameOptions } from './hooks/useProductNameOptions';

const ProductList = () => {
  const { formatMessage } = useLocale();
  const navigate = useNavigate();

  const {
    options: productNameOptions,
    loading: fetchingProductNameOptions,
    onDebounceSearch: onDebounceSearchProductName,
  } = useProductNameOptions();
  const {
    options: productCodeOptions,
    loading: fetchingProductCodeOptions,
    onDebounceSearch: onDebounceSearchProductCode,
  } = useProductCodeOptions();

  const {
    loading,
    data: productList,
    searchFormRef,
    pagination,
    handleTableChange,
    handleReset,
    handleSearch,
    handleRefresh,
  } = useSearchTable((searchFormValue, paginationConfig) => {
    console.log(searchFormValue, paginationConfig);

    return {
      total: 200,
      list: [
        {
          productCode: '1',
          productName: '稳赔不赚基金',
          establishmentDate: '2020-01-01',
          riskLevel: 'R1',
          shareType: 'A',
          benchmarkIndex: '沪深300',
          netValueDate: '2020-01-01',
          netValue: 1.01,
        },
      ],
    };
  });

  useEffect(() => {
    handleRefresh();
  }, []);

  /**
   * 跳转新建商品
   */
  const handleCreateProduct = () => {
    navigate('/products/create');
  };

  /**
   * 上架/下架产品
   */
  const handleUpdateProductStatus = (record: any) => {
    console.log('上架/下架');

    try {
      // TODO 请求接口更新状态
      message.success(formatMessage({ id: 'products.list.pullOffSuccessMsg' }));
      message.success(formatMessage({ id: 'products.list.putOnSuccessMsg' }));
      handleRefresh();
    } catch (error) {}
  };

  /** 表单过滤器列 */
  const filterColumns: SearchQueryFilterProps['columns'] = [
    {
      title: formatMessage({ id: 'products.list.productName' }),
      name: 'productName',
      render: () => (
        <Select
          options={productNameOptions}
          placeholder={`${formatMessage({ id: 'global.placeholder.selectPrefix' })}${formatMessage({
            id: 'products.list.productName',
          })}`}
          notFoundContent={fetchingProductNameOptions ? <Spin size="small" /> : undefined}
          suffixIcon={null}
          onSearch={onDebounceSearchProductName}
          allowClear
          showSearch
        />
      ),
    },
    {
      title: formatMessage({ id: 'products.list.productCode' }),
      name: 'productCode',
      render: () => (
        <Select
          options={productCodeOptions}
          placeholder={`${formatMessage({ id: 'global.placeholder.selectPrefix' })}${formatMessage({
            id: 'products.list.productCode',
          })}`}
          notFoundContent={fetchingProductCodeOptions ? <Spin size="small" /> : undefined}
          suffixIcon={null}
          onSearch={onDebounceSearchProductCode}
          allowClear
          showSearch
        />
      ),
    },
  ];

  /** 表单列 */
  const tableColumns = [
    {
      dataIndex: 'productName',
      title: formatMessage({ id: 'products.list.productName' }),
    },
    {
      dataIndex: 'productCode',
      title: formatMessage({ id: 'products.list.productCode' }),
    },
    {
      dataIndex: 'establishmentDate',
      title: formatMessage({ id: 'products.list.establishmentDate' }),
    },
    {
      dataIndex: 'riskLevel',
      title: formatMessage({ id: 'products.list.riskLevel' }),
    },
    {
      dataIndex: 'shareType',
      title: formatMessage({ id: 'products.list.shareType' }),
    },
    {
      dataIndex: 'benchmarkIndex',
      title: formatMessage({ id: 'products.list.benchmarkIndex' }),
    },
    {
      dataIndex: 'netValueDate',
      title: formatMessage({ id: 'products.list.netValueDate' }),
    },
    {
      dataIndex: 'netValue',
      title: formatMessage({ id: 'products.list.netValue' }),
    },
    {
      dataIndex: 'lockDate',
      title: formatMessage({ id: 'products.list.lockDate' }),
    },
    {
      title: formatMessage({ id: 'products.list.actions' }),
      render: (_text: any, record: any) => (
        <Space>
          <Button type="link" size="small" onClick={() => navigate(`/products/${record.productCode}`)}>
            {formatMessage({ id: 'products.list.viewDetails' })}
          </Button>
          <Button type="link" size="small" onClick={() => handleUpdateProductStatus(record)}>
            {formatMessage({ id: 'products.list.putOn' })}
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="product-list">
      <SearchQueryFilter
        ref={searchFormRef}
        columns={filterColumns}
        searchBtnProps={{
          loading: loading,
        }}
        onReset={handleReset}
        onSearch={handleSearch}
      />

      <div className="product-list-table">
        <Flex justify="end" style={{ marginBottom: 16 }}>
          <Button type="primary" onClick={handleCreateProduct}>
            {formatMessage({ id: 'global.tips.create' })}
          </Button>
        </Flex>

        <Table
          columns={tableColumns}
          dataSource={productList}
          rowKey="productCode"
          pagination={pagination}
          onChange={handleTableChange}
        />
      </div>
    </div>
  );
};

export default ProductList;
