/**
 * @Owners ljh
 * @Title 产品名称 options
 */
import { debounce } from 'lodash';
import { useMemo, useRef, useState } from 'react';

export const useProductNameOptions = () => {
  const fetchRef = useRef(0);
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<{ label: string; value: string }[]>([]);

  /**
   * 获取产品名称列表
   * @param value
   */
  const fetchProductNameList = async (value: string) => {
    // TODO 替换为真实接口
    //   const result = await fetchOptions(value);
    //   return result;

    return [
      { label: '稳赔不赚基金', value: '1' },
      { label: '稳赔不赚基金2', value: '2' },
      { label: '稳赔不赚基金3', value: '3' },
    ];
  };

  const onDebounceSearch = useMemo(() => {
    const loadOptions = (value: string) => {
      fetchRef.current += 1;
      const fetchId = fetchRef.current;

      setOptions([]);
      setLoading(true);

      fetchProductNameList(value).then(newOptions => {
        if (fetchId !== fetchRef.current) return;

        setOptions(newOptions);
        setLoading(false);
      });
    };

    return debounce(loadOptions, 300);
  }, []);

  return {
    options,
    loading,
    onDebounceSearch,
  };
};
