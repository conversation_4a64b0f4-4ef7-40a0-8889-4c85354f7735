/**
 * @Owners ljh
 * @Title 产品基础信息
 */
import './index.less';

import { PlusOutlined } from '@ant-design/icons';
import { Avatar, DatePicker, Input, InputNumber, Select, Upload } from 'antd';
import classNames from 'classnames';

import { DescriptionsForm, type DescriptionsFormProps } from '@/components';
import { useLocale } from '@/locales';

import {
  CLOSURE_PERIOD_MAP,
  CLOSURE_PERIOD_OPTIONS,
  RISK_LEVEL_MAP,
  RISK_LEVEL_OPTIONS,
  SHARE_TYPE_MAP,
  SHARE_TYPE_OPTIONS,
} from './consts';
import { useFormItemConfig } from './useFormItemConfig';

export interface ProductBasicInfoProps extends Omit<DescriptionsFormProps, 'items'> {
  dataSource?: any;
}

const ProductBasicInfo = (props: ProductBasicInfoProps) => {
  const { dataSource, className, ...restProps } = props;
  const { formatMessage } = useLocale();
  const { formItemsConfig } = useFormItemConfig();

  const uploadButton = (
    <button style={{ border: 0, background: 'none' }} type="button">
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </button>
  );

  const items: DescriptionsFormProps['items'] = [
    {
      label: formatMessage({ id: 'products.basicInfo.productName' }),
      children: (
        <DescriptionsForm.Field
          render={() => '稳赔不赚基金'}
          renderFormItem={() => <Input placeholder={formItemsConfig.productName.placeholder} maxLength={20} />}
          formItemName="productName"
          formItemProps={{
            rules: formItemsConfig.productName.rules,
          }}
        />
      ),
    },
    {
      label: formatMessage({ id: 'products.basicInfo.productCode' }),
      children: (
        <DescriptionsForm.Field
          render={() => '123456'}
          renderFormItem={() => (
            <Input
              placeholder={formatInputPlaceholder(
                formatMessage({
                  id: 'products.basicInfo.productCode',
                }),
              )}
              maxLength={20}
            />
          )}
        />
      ),
    },
    {
      label: formatMessage({ id: 'products.basicInfo.shareType' }),
      children: (
        <DescriptionsForm.Field
          render={() => SHARE_TYPE_MAP['A']}
          renderFormItem={() => (
            <Select
              options={SHARE_TYPE_OPTIONS}
              placeholder={formatSelectPlaceholder(formatMessage({ id: 'products.basicInfo.shareType' }))}
            />
          )}
        />
      ),
    },
    {
      label: formatMessage({ id: 'products.basicInfo.fundType' }),
      children: (
        <DescriptionsForm.Field
          render={() => '开放型基金'}
          renderFormItem={() => (
            <Input
              placeholder={formatInputPlaceholder(formatMessage({ id: 'products.basicInfo.fundType' }))}
              maxLength={20}
            />
          )}
        />
      ),
    },
    {
      label: formatMessage({ id: 'products.basicInfo.riskLevel' }),
      children: (
        <DescriptionsForm.Field
          render={() => RISK_LEVEL_MAP['R1']}
          renderFormItem={() => (
            <Select
              options={RISK_LEVEL_OPTIONS}
              placeholder={formatSelectPlaceholder(formatMessage({ id: 'products.basicInfo.riskLevel' }))}
            />
          )}
        />
      ),
    },
    {
      label: formatMessage({ id: 'products.basicInfo.strategyType' }),
      children: (
        <DescriptionsForm.Field
          render={() => '债券策略'}
          renderFormItem={() => (
            <Input
              placeholder={formatInputPlaceholder(formatMessage({ id: 'products.basicInfo.strategyType' }))}
              maxLength={20}
            />
          )}
        />
      ),
    },
    {
      label: formatMessage({ id: 'products.basicInfo.establishmentDate' }),
      children: (
        <DescriptionsForm.Field
          render={() => '2025-05-01'}
          renderFormItem={() => (
            <DatePicker
              placeholder={formatInputPlaceholder(formatMessage({ id: 'products.basicInfo.establishmentDate' }))}
            />
          )}
        />
      ),
    },
    {
      label: formatMessage({ id: 'products.basicInfo.openDate' }),
      children: (
        <DescriptionsForm.Field
          render={() => '每月25日'}
          renderFormItem={() => (
            <Input
              placeholder={formatInputPlaceholder(formatMessage({ id: 'products.basicInfo.openDate' }))}
              maxLength={20}
            />
          )}
        />
      ),
    },
    {
      label: formatMessage({ id: 'products.basicInfo.closurePeriod' }),
      children: (
        <DescriptionsForm.Field
          render={() => CLOSURE_PERIOD_MAP[0.5]}
          renderFormItem={() => (
            <Select
              options={CLOSURE_PERIOD_OPTIONS}
              placeholder={formatSelectPlaceholder(formatMessage({ id: 'products.basicInfo.closurePeriod' }))}
            />
          )}
        />
      ),
    },
    {
      label: formatMessage({ id: 'products.basicInfo.valuationMethod' }),
      children: (
        <DescriptionsForm.Field
          render={() => '6.0%-8.0% (费前)'}
          renderFormItem={() => (
            <Input
              placeholder={formatInputPlaceholder(formatMessage({ id: 'products.basicInfo.valuationMethod' }))}
              maxLength={20}
            />
          )}
        />
      ),
    },
    {
      label: formatMessage({ id: 'products.basicInfo.performanceBenchmark' }),
      children: (
        <DescriptionsForm.Field
          render={() => '6%'}
          renderFormItem={() => (
            <InputNumber
              suffix="%"
              placeholder={formatInputPlaceholder(formatMessage({ id: 'products.basicInfo.valuationMethod' }))}
              min={0}
              max={100}
              precision={2}
              style={{ minWidth: '100%' }}
              keyboard
            />
          )}
        />
      ),
    },
    {
      label: formatMessage({ id: 'products.basicInfo.managementFee' }),
      children: (
        <DescriptionsForm.Field
          render={() => '管理费0.8%'}
          renderFormItem={() => (
            <Input
              placeholder={formatInputPlaceholder(formatMessage({ id: 'products.basicInfo.managementFee' }))}
              maxLength={20}
            />
          )}
        />
      ),
    },
    {
      label: formatMessage({ id: 'products.basicInfo.subscriptionPeriod' }),
      children: (
        <DescriptionsForm.Field
          render={() => '每月开放申购'}
          renderFormItem={() => (
            <Input
              placeholder={formatInputPlaceholder(formatMessage({ id: 'products.basicInfo.subscriptionPeriod' }))}
              maxLength={20}
            />
          )}
        />
      ),
    },
    {
      label: formatMessage({ id: 'products.basicInfo.fundAdministration' }),
      children: (
        <DescriptionsForm.Field
          render={() => '中信证券'}
          renderFormItem={() => (
            <Input
              placeholder={formatInputPlaceholder(formatMessage({ id: 'products.basicInfo.fundAdministration' }))}
              maxLength={20}
            />
          )}
        />
      ),
    },
    {
      label: formatMessage({ id: 'products.basicInfo.depositoryBank' }),
      children: (
        <DescriptionsForm.Field
          render={() => '招商证券'}
          renderFormItem={() => (
            <Input
              placeholder={formatInputPlaceholder(formatMessage({ id: 'products.basicInfo.depositoryBank' }))}
              maxLength={20}
            />
          )}
        />
      ),
    },
    {
      label: formatMessage({ id: 'products.basicInfo.leadSeries' }),
      children: (
        <DescriptionsForm.Field
          render={() => 'series 002'}
          renderFormItem={() => (
            <Input
              placeholder={formatInputPlaceholder(formatMessage({ id: 'products.basicInfo.leadSeries' }))}
              maxLength={20}
            />
          )}
        />
      ),
      span: 'filled',
    },
    {
      label: formatMessage({ id: 'products.basicInfo.productFeatures' }),
      children: (
        <DescriptionsForm.Field
          render={() => '开放式境外基金|封闭期1年|50万美元起投'}
          renderFormItem={() => (
            <Input
              placeholder={formatInputPlaceholder(formatMessage({ id: 'products.basicInfo.productFeatures' }))}
              maxLength={30}
            />
          )}
        />
      ),
      span: 3,
    },
    {
      label: formatMessage({ id: 'products.basicInfo.productIntroduction' }),
      children: (
        <DescriptionsForm.Field
          render={() =>
            'Polaris Vega Fund以债券为主，构造兼具流动性和收益性的压舱，其中80%的资产投向高品质信用债、货币市场工具等;20%的资产投向进攻性资产，如利率债、可转债、美元债等。'
          }
          renderFormItem={() => (
            <Input.TextArea
              placeholder={formatInputPlaceholder(formatMessage({ id: 'products.basicInfo.productIntroduction' }))}
              maxLength={200}
            />
          )}
        />
      ),
      span: 3,
    },
    {
      label: formatMessage({ id: 'products.basicInfo.fundManager' }),
      children: (
        <DescriptionsForm.Field
          render={() => '易旸'}
          renderFormItem={() => (
            <Input
              placeholder={formatInputPlaceholder(formatMessage({ id: 'products.basicInfo.fundManager' }))}
              maxLength={20}
            />
          )}
        />
      ),
    },
    {
      label: formatMessage({ id: 'products.basicInfo.fundManagerTitle' }),
      children: (
        <DescriptionsForm.Field
          render={() => '创始人，基金经理'}
          renderFormItem={() => (
            <Input
              placeholder={formatInputPlaceholder(formatMessage({ id: 'products.basicInfo.fundManagerTitle' }))}
              maxLength={20}
            />
          )}
        />
      ),
    },
    {
      label: formatMessage({ id: 'products.basicInfo.fundManagerPhoto' }),
      children: (
        <DescriptionsForm.Field
          render={() => <Avatar />}
          renderFormItem={() => (
            <Upload name="avatar" listType="picture-card" showUploadList={false}>
              {uploadButton}
            </Upload>
          )}
        />
      ),
    },
    {
      label: formatMessage({ id: 'products.basicInfo.fundManagerIntroduction' }),
      children: (
        <DescriptionsForm.Field
          render={() =>
            '拥有十余年丰富的FICC及跨境衍生品销售交易管理经验，搭建团队管理资金头寸超百亿，作为市场主流交易商管理每年交易规模超1.3万亿、交易数量超2.8万笔。'
          }
          renderFormItem={() => (
            <Input.TextArea
              placeholder={formatInputPlaceholder(formatMessage({ id: 'products.basicInfo.fundManagerIntroduction' }))}
              maxLength={200}
            />
          )}
        />
      ),
      span: 3,
    },
    {
      label: formatMessage({ id: 'products.basicInfo.fundManagerCompany' }),
      children: (
        <DescriptionsForm.Field
          render={() => '北极星公司'}
          renderFormItem={() => (
            <Input
              placeholder={formatInputPlaceholder(formatMessage({ id: 'products.basicInfo.fundManagerCompany' }))}
              maxLength={20}
            />
          )}
        />
      ),
      span: 3,
    },
    {
      label: formatMessage({ id: 'products.basicInfo.fundManagerCompanyIntroduction' }),
      children: (
        <DescriptionsForm.Field
          render={() =>
            '获香港证监会SFC颁发的1号牌照(证券交易)、4号牌照(就证券提供意见)及9号牌照(提供资产管理)，是一家专注于固定收益领域的投资交易持牌金融机构。\n创始人团队拥有十余年国内、国际一线的“固收+“投资交易及研究经验，拥有丰富的市场交易资源和信用挖掘能力，擅长宏观配置和债一券投资。'
          }
          renderFormItem={() => (
            <Input.TextArea
              placeholder={formatInputPlaceholder(
                formatMessage({ id: 'products.basicInfo.fundManagerCompanyIntroduction' }),
              )}
              maxLength={200}
            />
          )}
        />
      ),
      span: 3,
    },
  ];

  return (
    <DescriptionsForm className={classNames('product-basic-info', className)} {...restProps} items={items} bordered />
  );
};

export default ProductBasicInfo;
