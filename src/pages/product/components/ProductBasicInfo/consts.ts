/**
 * @Owners ljh
 * @Title 产品基础信息常量
 */
import { type FormRule } from 'antd';

import { ClosurePeriod, RiskLevel, ShareType } from './types';

/** 份额类型 映射 */
export const SHARE_TYPE_MAP: { [key: string]: string } = {
  [ShareType.ClassA]: 'A',
  [ShareType.ClassB]: 'B',
};

/** 份额类型 选项 */
export const SHARE_TYPE_OPTIONS = Object.keys(SHARE_TYPE_MAP).map(key => ({
  value: key,
  label: SHARE_TYPE_MAP[key],
}));

/** 风险等级 映射 */
export const RISK_LEVEL_MAP: { [key: string]: string } = {
  [RiskLevel.R1]: 'R1',
  [RiskLevel.R2]: 'R2',
  [RiskLevel.R3]: 'R3',
  [RiskLevel.R4]: 'R4',
  [RiskLevel.R5]: 'R5',
};

/** 风险等级 选项 */
export const RISK_LEVEL_OPTIONS = Object.keys(RISK_LEVEL_MAP).map(key => ({
  value: key,
  label: RISK_LEVEL_MAP[key],
}));

/** 封闭期 映射 */
export const CLOSURE_PERIOD_MAP: { [key: number]: string } = {
  [ClosurePeriod.HalfYear]: '半年',
  [ClosurePeriod.OneYear]: '1年',
};

/** 封闭期 选项 */
export const CLOSURE_PERIOD_OPTIONS = Object.keys(CLOSURE_PERIOD_MAP).map(key => ({
  value: Number(key),
  label: CLOSURE_PERIOD_MAP[Number(key)],
}));
