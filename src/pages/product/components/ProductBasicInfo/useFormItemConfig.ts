/**
 * @Owners ljh
 * @Title 产品基础信息表单项配置
 */
import { useMemo } from 'react';

import { useLocale } from '@/locales';

export const useFormItemConfig = () => {
  const { formatMessage } = useLocale();

  /** 格式化输入框占位符 */
  const formatInputPlaceholder = (label: string) => {
    return `${formatMessage({ id: 'global.placeholder.inputPrefix' })}${label}`;
  };

  /** 格式化下拉框占位符 */
  const formatSelectPlaceholder = (label: string) => {
    return `${formatMessage({ id: 'global.placeholder.selectPrefix' })}${label}`;
  };

  const formItemsConfig = useMemo(
    () => ({
      productName: {
        label: formatMessage({ id: 'products.basicInfo.productName' }),
        placeholder: formatInputPlaceholder(formatMessage({ id: 'products.basicInfo.productName' })),
        rules: [
          { required: true, message: formatInputPlaceholder(formatMessage({ id: 'products.basicInfo.productName' })) },
        ],
      },
    }),
    [],
  );

  return {
    formItemsConfig,
    formatInputPlaceholder,
    formatSelectPlaceholder,
  };
};
