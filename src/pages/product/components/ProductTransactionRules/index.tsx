import { Input } from 'antd';

import { DescriptionsForm, type DescriptionsFormProps } from '@/components';
import { useLocale } from '@/locales';

export interface ProductTransactionRulesProps extends Omit<DescriptionsFormProps, 'items'> {
  dataSource?: any;
}

const ProductTransactionRules = (props: ProductTransactionRulesProps) => {
  const { dataSource, ...restProps } = props;
  const { formatMessage } = useLocale();

  const items: DescriptionsFormProps['items'] = [
    {
      label: formatMessage({ id: 'products.transactionRules.subscriptionStart' }),
      children: (
        <DescriptionsForm.Field
          render={() => '500000.00'}
          renderFormItem={() => <Input placeholder="请输入认购起投" />}
        />
      ),
    },
    {
      label: formatMessage({ id: 'products.transactionRules.subscriptionIncrement' }),
      children: (
        <DescriptionsForm.Field render={() => '100000'} renderFormItem={() => <Input placeholder="请输入认购递增" />} />
      ),
    },
    {
      label: formatMessage({ id: 'products.transactionRules.additionalSubscriptionStart' }),
      children: (
        <DescriptionsForm.Field render={() => '100000'} renderFormItem={() => <Input placeholder="请输入追加起投" />} />
      ),
    },
    {
      label: formatMessage({ id: 'products.transactionRules.additionalSubscriptionIncrement' }),
      children: (
        <DescriptionsForm.Field render={() => '100000'} renderFormItem={() => <Input placeholder="请输入追加递增" />} />
      ),
    },
    {
      label: formatMessage({ id: 'products.transactionRules.subscriptionFee' }),
      children: (
        <DescriptionsForm.Field render={() => '1%'} renderFormItem={() => <Input placeholder="请输入认购费率" />} />
      ),
    },
    {
      label: formatMessage({ id: 'products.transactionRules.minimumRedemption' }),
      children: (
        <DescriptionsForm.Field render={() => '100'} renderFormItem={() => <Input placeholder="请输入起赎份额" />} />
      ),
    },
    {
      label: formatMessage({ id: 'products.transactionRules.minimumHolding' }),
      children: (
        <DescriptionsForm.Field render={() => '10'} renderFormItem={() => <Input placeholder="请输入最低持有份额" />} />
      ),
    },
    {
      label: formatMessage({ id: 'products.transactionRules.redemptionFee' }),
      children: (
        <DescriptionsForm.Field render={() => '2%'} renderFormItem={() => <Input placeholder="请输入赎回费率" />} />
      ),
    },
    {
      label: formatMessage({ id: 'products.transactionRules.redemptionOpenPeriod' }),
      children: (
        <DescriptionsForm.Field
          render={() => '每月25日'}
          renderFormItem={() => <Input placeholder="请输入赎回开放期" />}
        />
      ),
    },
  ];

  return <DescriptionsForm {...restProps} items={items} bordered />;
};

export default ProductTransactionRules;
