import { Input } from 'antd';

import { DescriptionsForm, type DescriptionsFormProps } from '@/components';
import { useLocale } from '@/locales';

export interface ProductRaiseAccountInfoProps extends Omit<DescriptionsFormProps, 'items'> {
  dataSource?: any;
}

const ProductRaiseAccountInfo = (props: ProductRaiseAccountInfoProps) => {
  const { dataSource, ...restProps } = props;
  const { formatMessage } = useLocale();

  const items: DescriptionsFormProps['items'] = [
    {
      label: formatMessage({ id: 'products.raiseAccountInfo.accountName' }),
      children: (
        <DescriptionsForm.Field
          render={() => '明辰资本平衡基金-明辰资本维加基金'}
          renderFormItem={() => <Input placeholder="请输入账户名称" />}
        />
      ),
    },
    {
      label: formatMessage({ id: 'products.raiseAccountInfo.bankName' }),
      children: (
        <DescriptionsForm.Field
          render={() => '中国民生银行股份有限公司香港分行'}
          renderFormItem={() => <Input placeholder="请输入银行名称" />}
        />
      ),
    },
    {
      label: formatMessage({ id: 'products.raiseAccountInfo.bankAccount' }),
      children: (
        <DescriptionsForm.Field
          render={() => '5682866183933626625626'}
          renderFormItem={() => <Input placeholder="请输入银行账户" />}
        />
      ),
    },
    {
      label: formatMessage({ id: 'products.raiseAccountInfo.bicCode' }),
      children: (
        <DescriptionsForm.Field
          render={() => '**********'}
          renderFormItem={() => <Input placeholder="请输入BIC代码" />}
        />
      ),
    },
    {
      label: formatMessage({ id: 'products.raiseAccountInfo.remittanceNote' }),
      span: 'filled',
      children: (
        <DescriptionsForm.Field
          render={() => '承”投资者姓名“之命令，认购Polaris Balanced OFC-"产品名称'}
          renderFormItem={() => <Input.TextArea placeholder="请输入汇款备注" />}
        />
      ),
    },
  ];

  return <DescriptionsForm {...restProps} items={items} bordered />;
};

export default ProductRaiseAccountInfo;
