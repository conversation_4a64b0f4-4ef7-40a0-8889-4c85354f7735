/**
 * @Owners ljh
 * @Title 产品详情
 */
import './index.less';

import { Tabs, type TabsProps } from 'antd';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { PageHeader } from '@/components';
import { useLocale } from '@/locales';

import ProductBasicInfo from '../components/ProductBasicInfo';
import ProductRaiseAccountInfo from '../components/ProductRaiseAccountInfo';
import ProductTransactionRules from '../components/ProductTransactionRules';

enum DetailTabs {
  /** 基础信息 */
  BasicInfo = '1',
  /** 净值规模 */
  NetValueScale = '2',
  /** 交易规则 */
  TransactionRules = '3',
  /** 募集账号信息 */
  RaiseAccountInfo = '4',
}

const ProductDetail = () => {
  const { formatMessage } = useLocale();
  const navigate = useNavigate();
  /** 当前选中的tab */
  const [activeTabKey, setActiveTabKey] = useState<string>(DetailTabs.BasicInfo);

  const handleBack = () => {
    navigate(-1);
  };

  const items: TabsProps['items'] = [
    {
      key: DetailTabs.BasicInfo,
      label: formatMessage({ id: 'products.details.basicInfo' }),
      children: <ProductBasicInfo />,
    },
    {
      key: DetailTabs.NetValueScale,
      label: formatMessage({ id: 'products.details.netValueScale' }),
      children: '产品信息',
    },
    {
      key: DetailTabs.TransactionRules,
      label: formatMessage({ id: 'products.details.transactionRules' }),
      children: <ProductTransactionRules />,
    },
    {
      key: DetailTabs.RaiseAccountInfo,
      label: formatMessage({ id: 'products.details.raiseAccountInfo' }),
      children: <ProductRaiseAccountInfo />,
    },
  ];

  return (
    <div className="product-detail">
      <PageHeader title={formatMessage({ id: 'products.details.title' })} onBack={handleBack} />
      <div className="product-detail-container">
        <Tabs items={items} activeKey={activeTabKey} size="large" onChange={setActiveTabKey} />
      </div>
    </div>
  );
};

export default ProductDetail;
