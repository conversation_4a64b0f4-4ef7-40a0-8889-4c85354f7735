/**
 * @Owners ljh
 * @Title 产品创建步骤
 */
import type { ButtonProps, StepProps } from 'antd';
import type { ReactNode } from 'react';

import './index.less';

import { Button, Flex, Steps } from 'antd';
import classNames from 'classnames';
import { omit } from 'lodash';
import { useCallback, useMemo } from 'react';

import { Show } from '@/components';

export interface ProductStepsItem extends StepProps {
  children?: ReactNode;
}

export interface ProductStepsProps {
  className?: string;
  /** 当前步骤 */
  current?: number;
  /** 被隐藏时是否渲染 DOM 结构，，默认true */
  forceRender?: boolean;
  items?: ProductStepsItem[];
  /** 上一步按钮的文本, 默认上一步 */
  prevBtnText?: string | ((current: number) => string);
  /** 上一步按钮的props */
  prevBtnProps?: ButtonProps;
  /** 下一步按钮的文本，默认下一步 */
  nextBtnText?: string | ((current: number) => string);
  /** 下一步按钮的props */
  nextBtnProps?: ButtonProps;
  style?: React.CSSProperties;
  /** 点击切换步骤时触发 */
  onChange?: (prev: number, next: number, type: 'prev' | 'next') => void;
}

const ProductSteps = (props: ProductStepsProps) => {
  const {
    className,
    style,
    forceRender = true,
    items,
    current = 0,
    prevBtnText,
    prevBtnProps,
    nextBtnText,
    nextBtnProps,
    onChange,
  } = props;

  const stepsItems = useMemo(() => {
    return items?.map(item => omit(item, 'children')) || [];
  }, [items]);

  /** 上一步按钮文案 */
  const prevButtonText = useMemo(() => {
    if (typeof prevBtnText === 'function') {
      return prevBtnText(current);
    }

    return prevBtnText || '上一步';
  }, [prevBtnText, current]);

  /** 下一步按钮文案 */
  const nextButtonText = useMemo(() => {
    if (typeof nextBtnText === 'function') {
      return nextBtnText(current);
    }

    return nextBtnText || (current >= stepsItems.length - 1 ? '完成' : '下一步');
  }, [nextBtnText, current, stepsItems.length]);

  const handleBtnClick = useCallback(
    (type: 'prev' | 'next') => {
      onChange?.(
        current,
        type === 'prev' ? Math.max(0, current - 1) : Math.min(current + 1, (items?.length || 1) - 1),
        type,
      );
    },
    [current, stepsItems.length],
  );

  return (
    <div className={classNames('product-steps-wrapper', className)} style={style}>
      {/* S 步骤条 */}
      <Steps className="product-steps" items={stepsItems} current={current} />
      {/* E 步骤条 */}

      {/* S 步骤条主体 */}
      <div className="product-steps-container">
        {items?.map((item, index) => (
          <Show key={index} when={forceRender || index === current}>
            <div className={classNames('product-steps-item', { 'product-steps-item--hidden': index !== current })}>
              {item.children}
            </div>
          </Show>
        ))}
      </div>
      {/* E 步骤条主体 */}

      {/* S 步骤条底部 */}
      <Show when={!!stepsItems.length}>
        <Flex className="product-steps-footer" justify="center" gap={24}>
          <Show when={current > 0}>
            <Button {...prevBtnProps} onClick={() => handleBtnClick('prev')}>
              {prevButtonText}
            </Button>
          </Show>
          <Button type="primary" ghost {...nextBtnProps} onClick={() => handleBtnClick('next')}>
            {nextButtonText}
          </Button>
        </Flex>
      </Show>
      {/* E 步骤条底部 */}
    </div>
  );
};

export default ProductSteps;
