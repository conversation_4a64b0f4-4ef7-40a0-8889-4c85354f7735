/**
 * @Owners ljh
 * @Title 新建产品
 */
import './index.less';

import { Form } from 'antd';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { PageHeader } from '@/components';

import ProductBasicInfo from '../components/ProductBasicInfo';
import ProductRaiseAccountInfo from '../components/ProductRaiseAccountInfo';
import ProductTransactionRules from '../components/ProductTransactionRules';
import ProductSteps, { type ProductStepsProps } from './components/ProductSteps';

const ProductCreate = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(0);
  const [basicInfoForm] = Form.useForm();

  const handleStepsChange: ProductStepsProps['onChange'] = async (prev, next) => {
    await basicInfoForm?.validateFields();
    // setCurrentStep(next);
  };

  /** 返回 */
  const handleBack = () => {
    navigate(-1);
  };

  const items: ProductStepsProps['items'] = [
    {
      title: '基础信息',
      children: <ProductBasicInfo mode="edit" formProps={{ form: basicInfoForm }} />,
    },
    {
      title: '净值规模',
    },
    {
      title: '交易规则',
      children: <ProductTransactionRules mode="edit" />,
    },
    {
      title: '募集账号信息',
      children: <ProductRaiseAccountInfo mode="edit" />,
    },
  ];

  return (
    <div className="product-create">
      <PageHeader title="新建产品" onBack={handleBack} />

      <div className="product-create-container">
        <ProductSteps current={currentStep} items={items} onChange={handleStepsChange} />
      </div>
    </div>
  );
};

export default ProductCreate;
