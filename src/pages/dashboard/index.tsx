import type { FC } from 'react';

import './index.less';

import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

import Overview from './overview';
import SalePercent from './salePercent';
import TimeLine from './timeLine';

const DashBoardPage: FC = () => {
  const [loading, setLoading] = useState(true);

  const { productId } = useParams<{ productId: string }>();

  // mock timer to mimic dashboard data loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(undefined as any);
    }, 2000);

    return () => {
      clearTimeout(timer);
    };
  }, []);

  return (
    <div>
      {productId && (
        <h1 className="text-3 mb-4">
          产品 ID: <span className="text-primary">{productId}</span>
        </h1>
      )}
      <Overview loading={loading} />
      <SalePercent loading={loading} />
      <TimeLine loading={loading} />
    </div>
  );
};

export default DashBoardPage;
