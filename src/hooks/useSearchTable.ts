/**
 * @Owners ljh
 * @Title 搜索表格，需要搭配 SearchQueryFilter 组件使用
 */
import type { SearchQueryFilterRef } from '@/components';
import type { TablePaginationConfig } from 'antd';

import { useCallback, useMemo, useRef, useState } from 'react';

import { usePagination } from './usePagination';

export type SearchTableService<TData = any, TSearchFormValue = any> = (
  searchFormValue: TSearchFormValue,
  paginationConfig: { current: number; pageSize: number },
) =>
  | Promise<{
      total: number;
      list: TData;
    } | void>
  | void
  | {
      total: number;
      list: TData;
    };

export const useSearchTable = <TData = any, TSearchFormValue = any>(
  service: SearchTableService<TData, TSearchFormValue>,
) => {
  const { current, pageSize, total, setTotal, handlePageChange } = usePagination();
  /** 搜索过滤表单 */
  const searchFormRef = useRef<SearchQueryFilterRef>(null);
  /** 是否加载中，只有主动点击搜索才会修改 */
  const [loading, setLoading] = useState(false);
  /** 数据源 */
  const [data, setData] = useState<TData>();
  /** 请求的接口id */
  const fetchIdRef = useRef(0);

  /**
   * 请求接口
   */
  const fetchService: (...args: Parameters<SearchTableService<TData, TSearchFormValue>>) => Promise<void> = useCallback(
    async (searchFormValue, paginationConfig) => {
      fetchIdRef.current += 1;
      const fetchId = fetchIdRef.current;

      const res = await service(searchFormValue, paginationConfig);

      if (!res) return;
      if (fetchId !== fetchIdRef.current) return;

      setData(res?.list);
      setTotal(res?.total);
    },
    [service],
  );

  /** 分页配置 */
  const pagination = useMemo(() => {
    return {
      current,
      pageSize,
      total,
    };
  }, [current, pageSize, total]);

  /** 表格分页切换 */
  const handleTableChange = useCallback(
    async (paginationConfig: TablePaginationConfig) => {
      const { current = 1, pageSize = 20 } = paginationConfig;

      /** 获取当前搜索表达的值 */
      const formValue: TSearchFormValue = searchFormRef.current?.getSearchFieldsValue();

      handlePageChange(current, pageSize);
      await fetchService(formValue, { current: 1, pageSize });
    },
    [fetchService, handlePageChange],
  );

  /**
   * 查询
   */
  const handleSearch = useCallback(
    async (value: TSearchFormValue) => {
      if (loading) return;
      setLoading(true);

      try {
        handlePageChange(1, pageSize);
        await fetchService(value, { current: 1, pageSize });
      } catch (error) {
        throw error;
      } finally {
        setLoading(loading);
      }
    },
    [loading, handlePageChange, fetchService],
  );

  /**
   * 重置
   */
  const handleReset = useCallback(async () => {
    handlePageChange(1, pageSize);
    await fetchService({} as TSearchFormValue, { current: 1, pageSize });
  }, [fetchService, handlePageChange]);

  /**
   * 刷新
   */
  const handleRefresh = useCallback(async () => {
    /** 获取当前搜索表达的值 */
    const formValue: TSearchFormValue = searchFormRef.current?.getSearchFieldsValue();

    handlePageChange(1, pageSize);
    await fetchService(formValue, { current: 1, pageSize });
  }, [fetchService, handlePageChange]);

  return {
    loading,
    searchFormRef,
    pagination,
    data,
    handleTableChange,
    handleSearch,
    handleReset,
    handleRefresh,
  };
};
