/**
 * @Owners ljh
 * @Title 分页
 */
import { useState } from 'react';

/** 默认分页 */
export const DEFAULT_PAGE_SIZE = 20;

export const usePagination = () => {
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [total, setTotal] = useState(0);

  const handlePageChange = (page: number, size: number = DEFAULT_PAGE_SIZE) => {
    setCurrent(page);
    setPageSize(size);
  };

  return {
    current,
    pageSize,
    total,
    setTotal,
    handlePageChange,
  };
};
