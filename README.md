# 管理后台

基于 React + Vite + TypeScript + Ant Design 的管理后台系统。

## 环境配置

本项目支持多种环境配置，可以根据不同的需求启动不同的环境。

### 启动命令

| 命令               | 说明                             | API 行为                                                                         |
| ------------------ | -------------------------------- | -------------------------------------------------------------------------------- |
| `npm run dev`      | 开发环境（默认走后端 test 环境） | 默认使用 test 环境 API，如果接口方法中添加`mock: true`，则该接口会使用 mock 数据 |
| `npm run dev:mock` | 开发环境（全部使用 Apifox 接口） | 所有接口都使用 mock 数据（Apifox）                                               |
| `npm run dev:test` | 测试环境                         | 使用 test 环境 API，即使设置了`mock: true`也不会使用 mock 数据                   |
| `npm run dev:gray` | 灰度环境                         | 使用 gray 环境 API，即使设置了`mock: true`也不会使用 mock 数据                   |
| `npm run dev:prod` | 生产环境                         | 使用 prod 环境 API，即使设置了`mock: true`也不会使用 mock 数据                   |

### 构建命令

| 命令                 | 说明             |
| -------------------- | ---------------- |
| `npm run build`      | 构建开发环境版本 |
| `npm run build:test` | 构建测试环境版本 |
| `npm run build:gray` | 构建灰度环境版本 |
| `npm run build:prod` | 构建生产环境版本 |

## API 配置

### 环境 API 地址

不同环境使用不同的 API 地址：

- **Development**: `https://newtest-gateway.ifengqun.com`
- **Test**: `https://newtest-gateway.ifengqun.com`
- **Gray**: `https://fq-gw-gray.ifengqun.com`
- **Production**: `https://newprod.ifengqun.com`

### Mock API

Mock API 地址：`http://127.0.0.1:4523/m1/345161-308456-default`（Apifox）

### 如何使用 Mock 数据

有两种方式可以使用 Mock 数据：

1. **全局 Mock 模式**：使用 `npm run dev:mock` 启动项目，所有接口都将使用 Mock 数据
2. **单个接口 Mock**：在接口方法中添加 `mock: true` 参数

   ```typescript
   // 示例：使用mock数据的登录接口
   export const apiLogin = (data: LoginParams) =>
     request<LoginResult>('post', '/user/login', data, {
       mock: true,
     });

   // 示例：使用mock数据的GET请求
   export const getBusinessUserList = (params: any) =>
     request<PageData<BusinessUser>>('get', '/business/list', { ...params, mock: true });
   ```

### 环境限制

- **Mock 数据仅在开发环境（dev）中生效**
- 在测试环境（test）、灰度环境（gray）和生产环境（prod）中，即使设置了`mock: true`，也会忽略该设置，直接使用真实 API

## 项目配置

### 菜单配置

菜单配置位于 `src/config/menu.config.ts`，支持不同环境使用不同的菜单结构：

- **开发/测试/灰度环境**：完整菜单，包含所有功能和组件
- **生产环境**：简化菜单，只包含必要功能

### 通知配置

通知配置位于 `src/config/notice.config.ts`，支持不同环境使用不同的通知项：

- **开发/测试/灰度环境**：完整通知项，用于测试
- **生产环境**：精简通知项

## 开发工具

在开发环境中，系统提供了一个 DevTools 组件，可以方便地查看当前环境信息和切换 API 模式：

- 显示当前环境
- 显示原始环境变量值
- 显示配置信息（菜单、通知、API 模式）
- 切换 Mock API 模式（仅在开发环境中有效）

## 目录结构

```
├── src/
│   ├── api/                # API请求
│   ├── assets/             # 静态资源
│   ├── components/         # 公共组件
│   ├── config/             # 配置文件
│   │   ├── env.ts          # 环境配置
│   │   ├── menu.config.ts  # 菜单配置
│   │   └── notice.config.ts# 通知配置
│   ├── interface/          # TypeScript接口定义
│   ├── layouts/            # 布局组件
│   ├── pages/              # 页面组件
│   ├── routes/             # 路由配置
│   ├── stores/             # 状态管理
│   └── utils/              # 工具函数
├── scripts/                # 脚本文件
│   ├── check-env.js        # 检查环境变量
│   ├── switch-env.js       # 切换环境
│   └── use-mock-api.js     # 启用/禁用Mock API
├── public/                 # 公共资源
├── .env.local              # 本地环境变量（自动生成）
├── vite.config.ts          # Vite配置
└── package.json            # 项目配置
```

## 技术栈

- **React**: UI 库
- **TypeScript**: 类型系统
- **Vite**: 构建工具
- **Ant Design**: UI 组件库
- **Redux Toolkit**: 状态管理
- **React Router**: 路由管理
- **Axios**: HTTP 客户端
- **Less**: 样式编写
- **Recharts**: 图表库
