/**
 * Environment checker script
 * Usage: node scripts/check-env.js
 */

const fs = require('fs');
const path = require('path');

console.log('Checking environment variables...');

// Read .env.local if it exists
const envPath = path.join(__dirname, '..', '.env.local');

if (fs.existsSync(envPath)) {
  console.log('\n.env.local file exists:');
  const content = fs.readFileSync(envPath, 'utf8');

  console.log(content);
} else {
  console.log('\n.env.local file does not exist.');
}

// Check NODE_ENV
console.log('\nNode environment:');
console.log(`NODE_ENV=${process.env.NODE_ENV || 'not set'}`);

console.log('\nTo set the environment, run one of the following commands:');
console.log('npm run env:dev   - Switch to development environment');
console.log('npm run env:test  - Switch to test environment');
console.log('npm run env:gray  - Switch to gray environment');
console.log('npm run env:prod  - Switch to production environment');

console.log('\nTo use the mock API, run:');
console.log('npm run use:mock  - Enable mock API');
console.log('npm run use:no-mock - Disable mock API');

console.log('\nTo start the app with specific environment:');
console.log('npm run dev       - Start with current environment');
console.log('npm run dev:test  - Start with test environment');
console.log('npm run dev:mock  - Start with mock API enabled');
