/**
 * Environment switcher script
 * Usage: node scripts/switch-env.js [development|test|gray|production]
 */

const fs = require('fs');
const path = require('path');

// Available environments
const ENVIRONMENTS = ['development', 'test', 'gray', 'production'];

// Get environment from command line argument
const env = process.argv[2];

if (!env || !ENVIRONMENTS.includes(env)) {
  console.error(`Error: Please specify a valid environment: ${ENVIRONMENTS.join(', ')}`);
  process.exit(1);
}

// Create .env.local file with the selected environment
const envContent = `# Generated by switch-env.js
# This environment variable is used to determine which environment to use
VITE_APP_ENV=${env}

# Keep any existing environment variables below this line
`;

try {
  // Read existing .env.local if it exists
  let finalContent = envContent;
  const envPath = path.join(__dirname, '..', '.env.local');

  if (fs.existsSync(envPath)) {
    const existingContent = fs.readFileSync(envPath, 'utf8');
    // Extract lines that don't set VITE_APP_ENV
    const otherLines = existingContent
      .split('\n')
      .filter(
        line =>
          !line.startsWith('VITE_APP_ENV=') &&
          !line.startsWith('# Generated by') &&
          !line.startsWith('# This environment') &&
          !line.startsWith('# Keep any existing'),
      )
      .join('\n');

    if (otherLines.trim()) {
      finalContent += otherLines + '\n';
    }
  }

  fs.writeFileSync(envPath, finalContent);
  console.log(`Environment switched to: ${env}`);
  console.log(`Environment variable set: VITE_APP_ENV=${env}`);
  console.log('Run "npm run dev" to start the development server with this environment.');
} catch (error) {
  console.error('Error writing .env.local file:', error);
  process.exit(1);
}
