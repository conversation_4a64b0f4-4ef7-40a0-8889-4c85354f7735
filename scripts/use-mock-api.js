/**
 * Mock API switcher script
 * Usage: node scripts/use-mock-api.js [true|false]
 */

const fs = require('fs');
const path = require('path');

// Get mock mode from command line argument
const useMock = process.argv[2] === 'true';

// Create .env.local file with the mock API setting
const updateEnvFile = () => {
  try {
    // Read existing .env.local if it exists
    let finalContent = '';
    const envPath = path.join(__dirname, '..', '.env.local');
    let existingContent = '';

    if (fs.existsSync(envPath)) {
      existingContent = fs.readFileSync(envPath, 'utf8');
      // Extract lines that don't set VITE_USE_MOCK_API
      const otherLines = existingContent
        .split('\n')
        .filter(line => !line.startsWith('VITE_USE_MOCK_API='))
        .join('\n');

      finalContent = otherLines + '\n';
    }

    // Add the mock API setting
    finalContent += `# Mock API setting
VITE_USE_MOCK_API=${useMock}
`;

    fs.writeFileSync(envPath, finalContent);
    console.log(`Mock API ${useMock ? 'enabled' : 'disabled'}`);
    console.log(`Environment variable set: VITE_USE_MOCK_API=${useMock}`);
    console.log('Run "npm run dev" to start the development server with this setting.');
  } catch (error) {
    console.error('Error writing .env.local file:', error);
    process.exit(1);
  }
};

// Validate input
if (process.argv[2] !== 'true' && process.argv[2] !== 'false') {
  console.error('Error: Please specify either "true" or "false"');
  console.log('Usage: node scripts/use-mock-api.js [true|false]');
  process.exit(1);
}

updateEnvFile();
