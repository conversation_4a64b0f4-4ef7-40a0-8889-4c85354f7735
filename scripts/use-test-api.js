/**
 * <PERSON><PERSON><PERSON> to set a flag to use test API in development environment
 * Usage: node scripts/use-test-api.js [true|false]
 */

const fs = require('fs');
const path = require('path');

// Get flag from command line argument
const useTestApi = process.argv[2] === 'true';

// Create or update .env.local file with the USE_TEST_API flag
const envPath = path.join(__dirname, '..', '.env.local');
let envContent = '';

// Read existing .env.local if it exists
try {
  if (fs.existsSync(envPath)) {
    const existingContent = fs.readFileSync(envPath, 'utf8');
    // Remove any existing USE_TEST_API line

    envContent = existingContent
      .split('\n')
      .filter(line => !line.startsWith('VITE_USE_TEST_API='))
      .join('\n');

    // Make sure there's a newline at the end if content exists
    if (envContent.trim() !== '') {
      envContent += '\n';
    }
  }
} catch (error) {
  console.error('Error reading existing .env.local file:', error);
}

// Add the USE_TEST_API flag
envContent += `VITE_USE_TEST_API=${useTestApi}\n`;

try {
  fs.writeFileSync(envPath, envContent);
  console.log(`Test API flag set to: ${useTestApi}`);
  console.log('Run "npm run dev" to start the development server with this setting.');
} catch (error) {
  console.error('Error writing .env.local file:', error);
  process.exit(1);
}
