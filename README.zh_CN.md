[English](./README.md) | 简体中文

<h1 align="center">React Antd Admin</h1>

<div align="center">

React Hooks + Typescript 最佳实践。

<a href="https://github.com/facebook/react">
  <img src="https://img.shields.io/badge/react-17.0.0-brightgreen" alt="React">
</a>
<a href="https://github.com/ant-design/ant-design">
  <img src="https://img.shields.io/badge/ant--design-5.2.0-brightgreen" alt="ant-design">
</a>
<a href="https://github.com/microsoft/TypeScript" rel="nofollow">
  <img src="https://img.shields.io/badge/typescript-4.3.2-brightgreen" alt="Typescript">
</a>
<a href="https://github.com/reduxjs/redux" rel="nofollow">
  <img src="https://img.shields.io/badge/@reduxjs/toolkit-1.4.0-brightgreen" alt="Redux">
</a>
<a href="https://github.com/WinmezzZ/react-antd-admin/blob/master/LICENSE">
  <img src="https://img.shields.io/github/license/mashape/apistatus.svg" alt="license">
</a>

</div>

- 预览: https://react-admin.winme.dev
- 文档: https://react-admin.winme.dev/documentation

## ✨ 特性

- 💡 **TypeScript**: 应用程序级 JavaScript 的语言
- 📜 **区块**: 通过区块模板快速构建页面
- 💎 **Hooks**: 使用最新的 react hooks API 代替传统的 class API
- 📐 **常见设计模式**: 提炼自中后台应用的典型页面和场景
- 🚀 **最新技术栈**: 使用 React/hooks/Redux/antd/typescript 等前端前沿技术开发
- 📱 **响应式**: 针对不同屏幕大小设计
- 🎨 **主题**: 动态切换主题色
- 🌐 **国际化**: 内建业界通用的国际化方案
- ⚙️ **最佳实践**: 良好的工程实践助您持续产出高质量代码
- 🔢 **Mock 数据**: 实用的本地数据调试方案
- ✅ **使用**: 完整的文档和详细的注释，无阻碍使用

## 📦 下载

```bash
$ git clone https://github.com/WinmezzZ/react-antd-admin.git
$ cd react-antd-admin
$ npm install
$ npm start
```

## 🔨 构建

```bash
npm install
npm run build
```

此项目默认部署在根站点上，如果要部署在子站点上，则需要设置公共路径，在本项目根目录下的 `.env` 文件中添加 `PUBLIC_URL=YOUR_CHILD_PATH`。

## 🖥 浏览器支持

现代浏览器和 Internet Explorer 10+

| [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/edge/edge_48x48.png" alt="IE / Edge" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>IE / Edge | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/firefox/firefox_48x48.png" alt="Firefox" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Firefox | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/chrome/chrome_48x48.png" alt="Chrome" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Chrome | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/safari/safari_48x48.png" alt="Safari" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Safari | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/opera/opera_48x48.png" alt="Opera" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Opera | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/electron/electron_48x48.png" alt="Electron" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Electron |
| --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| IE11, Edge                                                                                                                                                                                                      | last 2 versions                                                                                                                                                                                                   | last 2 versions                                                                                                                                                                                               | last 2 versions                                                                                                                                                                                               | last 2 versions                                                                                                                                                                                           | last 2 versions                                                                                                                                                                                                       |

## 🤝 贡献

我们非常欢迎您的贡献，您可以通过以下方式与我们共建 😃

- 在你的公司或个人项目中使用 React Antd Admin
- 通过 [GitHub issues](https://github.com/WinmezzZ/react-antd-admin/issues) 报告 bug 或进行咨询。
- 提交 [Pull Request](https://github.com/WinmezzZ/react-antd-admin/pulls) 改进代码。

> 强烈推荐阅读 [《提问的智慧》](https://github.com/ryanhanwu/How-To-Ask-Questions-The-Smart-Way)、[《如何向开源社区提问题》](https://github.com/seajs/seajs/issues/545) 和 [《如何有效地报告 Bug》](http://www.chiark.greenend.org.uk/%7Esgtatham/bugs-cn.html)、[《如何向开源项目提交无法解答的问题》](https://zhuanlan.zhihu.com/p/25795393)，更好的问题更容易获得帮助。
